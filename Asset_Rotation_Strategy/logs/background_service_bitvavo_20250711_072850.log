2025-07-11 07:28:50,287 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Exchange-specific logging initialized for bitvavo
2025-07-11 07:28:50,514 - [<PERSON>ITVAVO] - root - INFO - Telegram command handlers registered
2025-07-11 07:28:50,515 - [<PERSON><PERSON><PERSON><PERSON>] - root - ERROR - Error in Telegram polling: set_wakeup_fd only works in main thread of the main interpreter
2025-07-11 07:28:50,515 - [BITVAVO] - root - WARNING - Continuing without Telegram bot polling. Notifications will still be sent.
2025-07-11 07:28:50,517 - [BITVAVO] - root - INFO - Telegram bot polling started
2025-07-11 07:28:50,517 - [BITVAVO] - root - INFO - Telegram notifier initialized with notification level: standard
2025-07-11 07:28:50,517 - [BITVAVO] - root - INFO - Telegram notification channel initialized
2025-07-11 07:28:50,518 - [<PERSON><PERSON>VA<PERSON>] - root - INFO - Successfully loaded templates using utf-8 encoding
2025-07-11 07:28:50,518 - [<PERSON><PERSON>VA<PERSON>] - root - INFO - <PERSON>aded 24 templates from file
2025-07-11 07:28:50,518 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Notification manager initialized with 1 channels
2025-07-11 07:28:50,518 - [BITVAVO] - root - INFO - Notification manager initialized
2025-07-11 07:28:50,518 - [BITVAVO] - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-07-11 07:28:50,518 - [BITVAVO] - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-07-11 07:28:50,518 - [BITVAVO] - root - INFO - Set up critical time windows for 1d timeframe
2025-07-11 07:28:50,518 - [BITVAVO] - root - INFO - Network watchdog initialized with 10s check interval
2025-07-11 07:28:50,519 - [BITVAVO] - root - INFO - Loaded recovery state from data/state/recovery_state.json
2025-07-11 07:28:50,519 - [BITVAVO] - root - INFO - No state file found at /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/data/state/data/state/background_service_20250627_214259.json.json
2025-07-11 07:28:50,519 - [BITVAVO] - root - INFO - Recovery manager initialized
2025-07-11 07:28:50,519 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-07-11 07:28:50,519 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'exchange_params': {'bitvavo': {'operator_id': 1001}}, 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'live', 'order_type': 'market', 'position_size_pct': 1, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-07-11 07:28:50,519 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:28:50,532 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:28:50,532 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:28:50,532 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:28:50,532 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:28:50,532 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:28:50,532 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:28:50,532 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:28:50,532 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:28:50,532 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:28:50,532 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:28:50,545 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:28:50,885 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-07-11 07:28:51,089 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-11 07:28:51,090 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:28:51,090 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:28:51,090 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:28:51,090 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:28:51,090 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:28:51,109 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:28:51,673 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-11 07:28:51,674 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:28:51,700 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:28:51,700 - [BITVAVO] - root - INFO - Trading executor initialized for bitvavo
2025-07-11 07:28:51,700 - [BITVAVO] - root - INFO - Trading mode: live
2025-07-11 07:28:51,700 - [BITVAVO] - root - INFO - Trading enabled: True
2025-07-11 07:28:51,700 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:28:51,700 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:28:51,700 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:28:51,700 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:28:51,700 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:28:51,712 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:28:52,076 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-07-11 07:28:52,251 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-11 07:28:52,251 - [BITVAVO] - root - INFO - Trading enabled in live mode
2025-07-11 07:28:52,548 - [BITVAVO] - root - INFO - Connected to bitvavo, balance: 4544.82 EUR
2025-07-11 07:28:52,548 - [BITVAVO] - root - INFO - Generated run ID: 20250711_072852
2025-07-11 07:28:52,548 - [BITVAVO] - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-07-11 07:28:52,548 - [BITVAVO] - root - INFO - Background service initialized
2025-07-11 07:28:52,549 - [BITVAVO] - root - INFO - Network watchdog started
2025-07-11 07:28:52,549 - [BITVAVO] - root - INFO - Network watchdog started
2025-07-11 07:28:52,550 - [BITVAVO] - root - INFO - Schedule set up for 1d timeframe
2025-07-11 07:28:52,551 - [BITVAVO] - root - INFO - Background service started
2025-07-11 07:28:52,553 - [BITVAVO] - root - INFO - Executing strategy (run #1)...
2025-07-11 07:28:52,555 - [BITVAVO] - root - INFO - Resetting daily trade counters for this strategy execution
2025-07-11 07:28:52,555 - [BITVAVO] - root - INFO - No trades recorded today (Max: 5)
2025-07-11 07:28:52,555 - [BITVAVO] - root - INFO - Initialized daily trades counter for 2025-07-11
2025-07-11 07:28:52,555 - [BITVAVO] - root - INFO - Creating snapshot for candle timestamp: 20250711
2025-07-11 07:28:52,639 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:28:53,568 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Unknown error in HTTP implementation: RuntimeError('<asyncio.locks.Event object at 0x7c15ee2c4650 [unset]> is bound to a different event loop')
2025-07-11 07:28:53,637 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:28:53,640 - [BITVAVO] - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-07-11 07:28:53,640 - [BITVAVO] - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-07-11 07:28:53,640 - [BITVAVO] - root - INFO - Using recent date for performance tracking: 2025-07-04
2025-07-11 07:28:53,642 - [BITVAVO] - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-07-11 07:28:53,692 - [BITVAVO] - root - INFO - Loaded 2152 rows of ETH/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,693 - [BITVAVO] - root - INFO - Last timestamp in cache for ETH/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,693 - [BITVAVO] - root - INFO - Expected last timestamp for ETH/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,693 - [BITVAVO] - root - INFO - Data is up to date for ETH/USDT
2025-07-11 07:28:53,695 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,712 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,712 - [BITVAVO] - root - INFO - Last timestamp in cache for BTC/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,712 - [BITVAVO] - root - INFO - Expected last timestamp for BTC/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,712 - [BITVAVO] - root - INFO - Data is up to date for BTC/USDT
2025-07-11 07:28:53,714 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,729 - [BITVAVO] - root - INFO - Loaded 1795 rows of SOL/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,730 - [BITVAVO] - root - INFO - Last timestamp in cache for SOL/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,730 - [BITVAVO] - root - INFO - Expected last timestamp for SOL/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,730 - [BITVAVO] - root - INFO - Data is up to date for SOL/USDT
2025-07-11 07:28:53,731 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,740 - [BITVAVO] - root - INFO - Loaded 800 rows of SUI/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,741 - [BITVAVO] - root - INFO - Last timestamp in cache for SUI/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,741 - [BITVAVO] - root - INFO - Expected last timestamp for SUI/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,741 - [BITVAVO] - root - INFO - Data is up to date for SUI/USDT
2025-07-11 07:28:53,742 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,760 - [BITVAVO] - root - INFO - Loaded 2152 rows of XRP/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,760 - [BITVAVO] - root - INFO - Last timestamp in cache for XRP/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,761 - [BITVAVO] - root - INFO - Expected last timestamp for XRP/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,761 - [BITVAVO] - root - INFO - Data is up to date for XRP/USDT
2025-07-11 07:28:53,762 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,777 - [BITVAVO] - root - INFO - Loaded 1730 rows of AAVE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,777 - [BITVAVO] - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,778 - [BITVAVO] - root - INFO - Expected last timestamp for AAVE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,778 - [BITVAVO] - root - INFO - Data is up to date for AAVE/USDT
2025-07-11 07:28:53,778 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,794 - [BITVAVO] - root - INFO - Loaded 1753 rows of AVAX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,795 - [BITVAVO] - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,795 - [BITVAVO] - root - INFO - Expected last timestamp for AVAX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,795 - [BITVAVO] - root - INFO - Data is up to date for AVAX/USDT
2025-07-11 07:28:53,796 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,814 - [BITVAVO] - root - INFO - Loaded 2152 rows of ADA/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,815 - [BITVAVO] - root - INFO - Last timestamp in cache for ADA/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,815 - [BITVAVO] - root - INFO - Expected last timestamp for ADA/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,815 - [BITVAVO] - root - INFO - Data is up to date for ADA/USDT
2025-07-11 07:28:53,816 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,834 - [BITVAVO] - root - INFO - Loaded 2152 rows of LINK/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,835 - [BITVAVO] - root - INFO - Last timestamp in cache for LINK/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,835 - [BITVAVO] - root - INFO - Expected last timestamp for LINK/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,835 - [BITVAVO] - root - INFO - Data is up to date for LINK/USDT
2025-07-11 07:28:53,836 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,853 - [BITVAVO] - root - INFO - Loaded 2152 rows of TRX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,853 - [BITVAVO] - root - INFO - Last timestamp in cache for TRX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,854 - [BITVAVO] - root - INFO - Expected last timestamp for TRX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,854 - [BITVAVO] - root - INFO - Data is up to date for TRX/USDT
2025-07-11 07:28:53,855 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,865 - [BITVAVO] - root - INFO - Loaded 798 rows of PEPE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,865 - [BITVAVO] - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,865 - [BITVAVO] - root - INFO - Expected last timestamp for PEPE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,865 - [BITVAVO] - root - INFO - Data is up to date for PEPE/USDT
2025-07-11 07:28:53,866 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,884 - [BITVAVO] - root - INFO - Loaded 2152 rows of DOGE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,884 - [BITVAVO] - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,885 - [BITVAVO] - root - INFO - Expected last timestamp for DOGE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,885 - [BITVAVO] - root - INFO - Data is up to date for DOGE/USDT
2025-07-11 07:28:53,886 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,905 - [BITVAVO] - root - INFO - Loaded 2152 rows of BNB/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,906 - [BITVAVO] - root - INFO - Last timestamp in cache for BNB/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,906 - [BITVAVO] - root - INFO - Expected last timestamp for BNB/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,907 - [BITVAVO] - root - INFO - Data is up to date for BNB/USDT
2025-07-11 07:28:53,908 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,923 - [BITVAVO] - root - INFO - Loaded 1788 rows of DOT/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:53,923 - [BITVAVO] - root - INFO - Last timestamp in cache for DOT/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,924 - [BITVAVO] - root - INFO - Expected last timestamp for DOT/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:28:53,924 - [BITVAVO] - root - INFO - Data is up to date for DOT/USDT
2025-07-11 07:28:53,925 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:53,927 - [BITVAVO] - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-07-11 07:28:53,927 - [BITVAVO] - root - INFO - MTPI Multi-Indicator Configuration:
2025-07-11 07:28:53,928 - [BITVAVO] - root - INFO -   - Number of indicators: 8
2025-07-11 07:28:53,928 - [BITVAVO] - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:28:53,928 - [BITVAVO] - root - INFO -   - Combination method: consensus
2025-07-11 07:28:53,928 - [BITVAVO] - root - INFO -   - Long threshold: 0.1
2025-07-11 07:28:53,928 - [BITVAVO] - root - INFO -   - Short threshold: -0.1
2025-07-11 07:28:53,929 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 07:28:53,929 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:28:53,929 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-07-11 07:28:53,929 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-07-11 07:28:53,929 - [BITVAVO] - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-07-11 07:28:53,929 - [BITVAVO] - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-07-11 07:28:53,929 - [BITVAVO] - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-07-11 07:28:53,930 - [BITVAVO] - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-07-11 07:28:53,930 - [BITVAVO] - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-07-11 07:28:53,930 - [BITVAVO] - root - INFO - n_assets=1, use_weighted_allocation=False
2025-07-11 07:28:53,930 - [BITVAVO] - root - INFO - Using provided trend method: PGO For Loop
2025-07-11 07:28:53,930 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:28:53,944 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:28:53,944 - [BITVAVO] - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-07-11 07:28:53,955 - [BITVAVO] - root - INFO - Configuration saved successfully.
2025-07-11 07:28:53,955 - [BITVAVO] - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 07:28:53,956 - [BITVAVO] - root - INFO - Number of trend detection assets: 14
2025-07-11 07:28:53,956 - [BITVAVO] - root - INFO - Selected assets type: <class 'list'>
2025-07-11 07:28:53,956 - [BITVAVO] - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:28:53,956 - [BITVAVO] - root - INFO - Number of trading assets: 14
2025-07-11 07:28:53,957 - [BITVAVO] - root - INFO - Trading assets type: <class 'list'>
2025-07-11 07:28:54,109 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:28:54,125 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:28:54,149 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:28:54,168 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:28:54,169 - [BITVAVO] - root - INFO - Execution context: backtesting
2025-07-11 07:28:54,169 - [BITVAVO] - root - INFO - Execution timing: candle_close
2025-07-11 07:28:54,169 - [BITVAVO] - root - INFO - Ratio calculation method: independent
2025-07-11 07:28:54,170 - [BITVAVO] - root - INFO - Tie-breaking strategy: imcumbent
2025-07-11 07:28:54,170 - [BITVAVO] - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-07-11 07:28:54,170 - [BITVAVO] - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:28:54,170 - [BITVAVO] - root - INFO - MTPI combination method override: consensus
2025-07-11 07:28:54,170 - [BITVAVO] - root - INFO - MTPI long threshold override: 0.1
2025-07-11 07:28:54,170 - [BITVAVO] - root - INFO - MTPI short threshold override: -0.1
2025-07-11 07:28:54,170 - [BITVAVO] - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-07-11 07:28:54,171 - [BITVAVO] - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-11 07:28:54,173 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,173 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,174 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,174 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,174 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,176 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,176 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,176 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,176 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,177 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,177 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,178 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,178 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,179 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:28:54,179 - [BITVAVO] - root - INFO - Checking cache for 14 symbols (1d)...
2025-07-11 07:28:54,210 - [BITVAVO] - root - INFO - Loaded 211 rows of ETH/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,212 - [BITVAVO] - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,213 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,213 - [BITVAVO] - root - INFO - Loaded 211 rows of ETH/USDT data from cache (after filtering).
2025-07-11 07:28:54,241 - [BITVAVO] - root - INFO - Loaded 211 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,242 - [BITVAVO] - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,244 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,245 - [BITVAVO] - root - INFO - Loaded 211 rows of BTC/USDT data from cache (after filtering).
2025-07-11 07:28:54,274 - [BITVAVO] - root - INFO - Loaded 211 rows of SOL/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,275 - [BITVAVO] - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,276 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,276 - [BITVAVO] - root - INFO - Loaded 211 rows of SOL/USDT data from cache (after filtering).
2025-07-11 07:28:54,286 - [BITVAVO] - root - INFO - Loaded 211 rows of SUI/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,287 - [BITVAVO] - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,288 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,288 - [BITVAVO] - root - INFO - Loaded 211 rows of SUI/USDT data from cache (after filtering).
2025-07-11 07:28:54,306 - [BITVAVO] - root - INFO - Loaded 211 rows of XRP/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,308 - [BITVAVO] - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,308 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,308 - [BITVAVO] - root - INFO - Loaded 211 rows of XRP/USDT data from cache (after filtering).
2025-07-11 07:28:54,326 - [BITVAVO] - root - INFO - Loaded 211 rows of AAVE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,327 - [BITVAVO] - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,327 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,328 - [BITVAVO] - root - INFO - Loaded 211 rows of AAVE/USDT data from cache (after filtering).
2025-07-11 07:28:54,344 - [BITVAVO] - root - INFO - Loaded 211 rows of AVAX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,345 - [BITVAVO] - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,345 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,345 - [BITVAVO] - root - INFO - Loaded 211 rows of AVAX/USDT data from cache (after filtering).
2025-07-11 07:28:54,365 - [BITVAVO] - root - INFO - Loaded 211 rows of ADA/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,366 - [BITVAVO] - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,367 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,367 - [BITVAVO] - root - INFO - Loaded 211 rows of ADA/USDT data from cache (after filtering).
2025-07-11 07:28:54,386 - [BITVAVO] - root - INFO - Loaded 211 rows of LINK/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,387 - [BITVAVO] - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,387 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,388 - [BITVAVO] - root - INFO - Loaded 211 rows of LINK/USDT data from cache (after filtering).
2025-07-11 07:28:54,408 - [BITVAVO] - root - INFO - Loaded 211 rows of TRX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,409 - [BITVAVO] - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,410 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,410 - [BITVAVO] - root - INFO - Loaded 211 rows of TRX/USDT data from cache (after filtering).
2025-07-11 07:28:54,421 - [BITVAVO] - root - INFO - Loaded 211 rows of PEPE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,422 - [BITVAVO] - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,423 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,423 - [BITVAVO] - root - INFO - Loaded 211 rows of PEPE/USDT data from cache (after filtering).
2025-07-11 07:28:54,446 - [BITVAVO] - root - INFO - Loaded 211 rows of DOGE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,447 - [BITVAVO] - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,448 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,448 - [BITVAVO] - root - INFO - Loaded 211 rows of DOGE/USDT data from cache (after filtering).
2025-07-11 07:28:54,468 - [BITVAVO] - root - INFO - Loaded 211 rows of BNB/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,469 - [BITVAVO] - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,470 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,470 - [BITVAVO] - root - INFO - Loaded 211 rows of BNB/USDT data from cache (after filtering).
2025-07-11 07:28:54,488 - [BITVAVO] - root - INFO - Loaded 211 rows of DOT/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,489 - [BITVAVO] - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:28:54,489 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,490 - [BITVAVO] - root - INFO - Loaded 211 rows of DOT/USDT data from cache (after filtering).
2025-07-11 07:28:54,490 - [BITVAVO] - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 07:28:54,490 - [BITVAVO] - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,490 - [BITVAVO] - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,491 - [BITVAVO] - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,491 - [BITVAVO] - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,491 - [BITVAVO] - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,491 - [BITVAVO] - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,491 - [BITVAVO] - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,491 - [BITVAVO] - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,491 - [BITVAVO] - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,492 - [BITVAVO] - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,492 - [BITVAVO] - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,492 - [BITVAVO] - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,492 - [BITVAVO] - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,492 - [BITVAVO] - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:28:54,537 - [BITVAVO] - root - INFO - Using standard MTPI warmup period of 120 days
2025-07-11 07:28:54,538 - [BITVAVO] - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-11 07:28:54,538 - [BITVAVO] - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-07-11 07:28:54,538 - [BITVAVO] - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-11 07:28:54,538 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:28:54,564 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:28:54,565 - [BITVAVO] - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-07-11 07:28:54,566 - [BITVAVO] - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-11 07:28:54,566 - [BITVAVO] - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:28:54,567 - [BITVAVO] - root - INFO - Override: combination_method = consensus
2025-07-11 07:28:54,567 - [BITVAVO] - root - INFO - Override: long_threshold = 0.1
2025-07-11 07:28:54,567 - [BITVAVO] - root - INFO - Override: short_threshold = -0.1
2025-07-11 07:28:54,568 - [BITVAVO] - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-07-11 07:28:54,568 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-11 07:28:54,569 - [BITVAVO] - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-07-11 07:28:54,569 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-11 07:28:54,590 - [BITVAVO] - root - INFO - Loaded 271 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:28:54,591 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:28:54,592 - [BITVAVO] - root - INFO - Loaded 271 rows of BTC/USDT data from cache (after filtering).
2025-07-11 07:28:54,592 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 07:28:54,592 - [BITVAVO] - root - INFO - Fetched BTC data: 271 candles from 2024-10-13 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:28:54,592 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-11 07:28:54,642 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(111), 0: np.int64(34), 1: np.int64(126)}
2025-07-11 07:28:54,642 - [BITVAVO] - root - INFO - Generated pgo signals: 271 values
2025-07-11 07:28:54,643 - [BITVAVO] - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-07-11 07:28:54,643 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-11 07:28:54,660 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(109), 0: np.int64(32), 1: np.int64(130)}
2025-07-11 07:28:54,660 - [BITVAVO] - root - INFO - Generated Bollinger Band signals: 271 values
2025-07-11 07:28:54,661 - [BITVAVO] - root - INFO - Generated bollinger_bands signals: 271 values
2025-07-11 07:28:55,320 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-11 07:28:55,320 - [BITVAVO] - root - INFO - Generated dwma_score signals: 271 values
2025-07-11 07:28:55,403 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-11 07:28:55,403 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(156), 0: np.int64(1), 1: np.int64(114)}
2025-07-11 07:28:55,403 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-11 07:28:55,403 - [BITVAVO] - root - INFO - Generated dema_super_score signals: 271 values
2025-07-11 07:28:55,581 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-11 07:28:55,582 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(105), 0: np.int64(87), 1: np.int64(79)}
2025-07-11 07:28:55,582 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-11 07:28:55,582 - [BITVAVO] - root - INFO - Generated dpsd_score signals: 271 values
2025-07-11 07:28:55,598 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-11 07:28:55,598 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-11 07:28:55,598 - [BITVAVO] - root - INFO - Generated aad_score signals: 271 values
2025-07-11 07:28:55,699 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-11 07:28:55,699 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-11 07:28:55,699 - [BITVAVO] - root - INFO - Generated dynamic_ema_score signals: 271 values
2025-07-11 07:28:55,882 - [BITVAVO] - root - INFO - Generated quantile_dema_score signals: 271 values
2025-07-11 07:28:55,893 - [BITVAVO] - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-07-11 07:28:55,894 - [BITVAVO] - root - INFO - Signal distribution: {1: 146, -1: 124, 0: 1}
2025-07-11 07:28:55,894 - [BITVAVO] - root - INFO - Generated combined MTPI signals: 271 values using consensus method
2025-07-11 07:28:55,895 - [BITVAVO] - root - INFO - Signal distribution: {1: 146, -1: 124, 0: 1}
2025-07-11 07:28:55,895 - [BITVAVO] - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-07-11 07:28:55,898 - [BITVAVO] - root - INFO - Saving configuration to config/settings.yaml...
2025-07-11 07:28:55,905 - [BITVAVO] - root - INFO - Configuration saved successfully.
2025-07-11 07:28:55,905 - [BITVAVO] - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-07-11 07:28:55,905 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:28:55,917 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:28:55,917 - [BITVAVO] - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-07-11 07:28:55,917 - [BITVAVO] - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-07-11 07:28:55,917 - [BITVAVO] - root - INFO - Using ratio calculation method: independent
2025-07-11 07:28:55,949 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:55,987 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:28:56,023 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:28:56,023 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,058 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:28:56,068 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:28:56,102 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:28:56,103 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,131 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:28:56,141 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:28:56,180 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:28:56,180 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,208 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:28:56,218 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:28:56,257 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:28:56,257 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,289 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:28:56,300 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:28:56,335 - [BITVAVO] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-11 07:28:56,336 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,369 - [BITVAVO] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-11 07:28:56,378 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:28:56,413 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:28:56,414 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,444 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:28:56,454 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:28:56,488 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:28:56,488 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,519 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:28:56,529 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:28:56,563 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:28:56,563 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,596 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:28:56,606 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:28:56,643 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:28:56,644 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,678 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:28:56,689 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:28:56,732 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:28:56,732 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,767 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:28:56,781 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:28:56,826 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,882 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:28:56,928 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:28:56,928 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:56,969 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:28:56,983 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:28:57,025 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:28:57,025 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,057 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:28:57,071 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:28:57,109 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,151 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:28:57,188 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:28:57,188 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,217 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:28:57,226 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:28:57,258 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:28:57,258 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,297 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:28:57,307 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:28:57,342 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:28:57,342 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,378 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:28:57,390 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:28:57,428 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:28:57,428 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,460 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:28:57,471 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:28:57,510 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:28:57,510 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,545 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:28:57,555 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:28:57,593 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:28:57,594 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,627 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:28:57,637 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:28:57,682 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:28:57,683 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,726 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:28:57,740 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:28:57,785 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:28:57,785 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,827 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:28:57,841 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:28:57,887 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:57,944 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:28:57,989 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,045 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:28:58,090 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:28:58,090 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,129 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:28:58,139 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:28:58,175 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-11 07:28:58,175 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,209 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-11 07:28:58,221 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:28:58,257 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,299 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:28:58,334 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:28:58,334 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,367 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:28:58,378 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:28:58,413 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:28:58,413 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,446 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:28:58,457 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:28:58,492 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-11 07:28:58,492 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,526 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-11 07:28:58,538 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:28:58,576 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:28:58,576 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,608 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:28:58,617 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:28:58,650 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:28:58,650 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,679 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:28:58,688 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:28:58,720 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:28:58,721 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,752 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:28:58,762 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:28:58,797 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:28:58,797 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,836 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:28:58,848 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:28:58,892 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:28:58,892 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:58,929 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:28:58,941 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:28:58,980 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:28:58,980 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,013 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:28:59,024 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:28:59,061 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:28:59,061 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,091 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:28:59,101 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:28:59,135 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:28:59,135 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,164 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:28:59,173 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:28:59,208 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:28:59,208 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,239 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:28:59,249 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:28:59,284 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,322 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:28:59,358 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,398 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:28:59,432 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,472 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:28:59,504 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:28:59,504 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,534 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:28:59,544 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:28:59,576 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,613 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:28:59,644 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,684 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:28:59,716 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:28:59,716 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,746 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:28:59,756 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:28:59,789 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,831 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:28:59,862 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,899 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:28:59,932 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:28:59,932 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:28:59,965 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:28:59,979 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:29:00,017 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,058 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:29:00,091 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,127 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:29:00,166 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:29:00,166 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,200 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:29:00,213 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:29:00,250 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,290 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:29:00,325 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,366 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:29:00,409 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:29:00,409 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,449 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:29:00,460 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:29:00,503 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:29:00,503 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,534 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:29:00,543 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:29:00,576 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:29:00,576 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,606 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:29:00,616 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:29:00,652 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:29:00,652 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,679 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:29:00,688 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:29:00,720 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:29:00,721 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,757 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:29:00,770 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:29:00,810 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:00,810 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,837 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:00,847 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:29:00,883 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:29:00,883 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:00,913 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:29:00,922 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:29:00,961 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,007 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:29:01,044 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,083 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:29:01,119 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:29:01,119 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,153 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:29:01,167 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:29:01,207 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,246 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:29:01,281 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,322 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:29:01,357 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,395 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:29:01,428 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,475 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:29:01,517 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,577 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:29:01,630 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:29:01,630 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,676 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:29:01,687 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:29:01,727 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:01,727 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,755 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:01,765 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:29:01,801 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:29:01,801 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,830 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:29:01,840 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:29:01,873 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,912 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:29:01,948 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:01,986 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:29:02,024 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:29:02,024 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,054 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:29:02,063 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:29:02,095 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,136 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:29:02,180 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:02,180 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,214 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:02,223 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:29:02,258 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,302 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:29:02,335 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,377 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:29:02,414 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,464 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:29:02,499 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,549 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:29:02,589 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,636 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:29:02,674 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:29:02,674 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,708 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:29:02,718 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:29:02,757 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,806 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:29:02,856 - [BITVAVO] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-11 07:29:02,856 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:02,907 - [BITVAVO] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-11 07:29:02,918 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:29:02,960 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,002 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:29:03,039 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,080 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:29:03,122 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,158 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:29:03,196 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,234 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:29:03,270 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,309 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:29:03,343 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:03,343 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,371 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:03,380 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:29:03,414 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,455 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:29:03,487 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,522 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:29:03,563 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:29:03,563 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,594 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:29:03,603 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:29:03,643 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,679 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:29:03,732 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:29:03,732 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,768 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:29:03,782 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:29:03,829 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:29:03,829 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,864 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:29:03,874 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:29:03,909 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:29:03,910 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:03,944 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:29:03,955 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:29:03,989 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,029 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:29:04,063 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:04,063 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,091 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:04,100 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:29:04,132 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:04,133 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,162 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:04,172 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:29:04,207 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,244 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:29:04,279 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:04,279 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,307 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:04,317 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:29:04,355 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,394 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:29:04,427 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,466 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:29:04,505 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,544 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:29:04,585 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,625 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:29:04,659 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,699 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:29:04,731 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:29:04,731 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,761 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:29:04,769 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:29:04,800 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,837 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:29:04,867 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,908 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:29:04,942 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:04,980 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:29:05,016 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:05,016 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,051 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:05,060 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:29:05,092 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,132 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:29:05,169 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,214 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:29:05,249 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,291 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:29:05,329 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,367 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:29:05,401 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,439 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:29:05,473 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:05,473 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,502 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:05,511 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:29:05,543 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,581 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:29:05,618 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:05,618 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,645 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:05,655 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:29:05,687 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:29:05,687 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,718 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:29:05,728 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:29:05,765 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:29:05,766 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,802 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:29:05,814 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:29:05,850 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,887 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:29:05,923 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:29:05,923 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:05,955 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:29:05,964 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:29:05,997 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:29:05,997 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,028 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:29:06,038 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:29:06,077 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,122 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:29:06,160 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,214 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:29:06,260 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,315 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:29:06,360 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,416 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:29:06,466 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,520 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:29:06,566 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,620 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:29:06,667 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,722 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:29:06,768 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,824 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:29:06,869 - [BITVAVO] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-11 07:29:06,869 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,901 - [BITVAVO] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-11 07:29:06,915 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:29:06,956 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:29:06,957 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:06,987 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:29:06,997 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:29:07,032 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:07,032 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,061 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:29:07,073 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:29:07,110 - [BITVAVO] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-11 07:29:07,110 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,141 - [BITVAVO] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-11 07:29:07,150 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:29:07,183 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,220 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:29:07,252 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,289 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:29:07,322 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,359 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:29:07,392 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,429 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:29:07,462 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,500 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:29:07,533 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,572 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:29:07,606 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,650 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:29:07,687 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:29:07,687 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,718 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:29:07,729 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:29:07,765 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:29:07,766 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,797 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:29:07,808 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:29:07,846 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:07,847 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,885 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:07,896 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:29:07,929 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:29:07,929 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:07,964 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:29:07,974 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:29:08,011 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:29:08,012 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,052 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:29:08,066 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:29:08,113 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:29:08,113 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,145 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:29:08,155 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:29:08,188 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:29:08,188 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,222 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:29:08,232 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:29:08,268 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:29:08,268 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,300 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:29:08,310 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:29:08,344 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,391 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:29:08,424 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:08,424 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,451 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:08,460 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:29:08,490 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,527 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:29:08,560 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,599 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:29:08,629 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:08,630 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,658 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:08,669 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:29:08,705 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:08,705 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,738 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:08,747 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:29:08,780 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:29:08,781 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,808 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:29:08,817 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:29:08,848 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:29:08,848 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,879 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:29:08,892 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:29:08,941 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:29:08,941 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:08,986 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:29:09,000 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:29:09,036 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:09,036 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,066 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:09,076 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:29:09,108 - [BITVAVO] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-11 07:29:09,108 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,138 - [BITVAVO] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-11 07:29:09,147 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:29:09,181 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:29:09,181 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,211 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:29:09,220 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:29:09,254 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:09,254 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,287 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:09,298 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:29:09,336 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,375 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:29:09,409 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,453 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:29:09,491 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,530 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:29:09,564 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,603 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:29:09,639 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,678 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:29:09,714 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:09,714 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,749 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:29:09,758 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:29:09,793 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:29:09,793 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,824 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:29:09,833 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:29:09,879 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:29:09,879 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,916 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:29:09,925 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:29:09,959 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:29:09,959 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:09,987 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:29:09,997 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:29:10,034 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-11 07:29:10,034 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:10,063 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-11 07:29:10,074 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:29:10,109 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:29:10,110 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:10,139 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:29:10,148 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:29:10,182 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:10,225 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:29:10,260 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:10,261 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:10,289 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:29:10,298 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:29:10,332 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:10,370 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:29:10,401 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:29:10,440 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:29:14,281 - [BITVAVO] - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-07-11 07:29:14,281 - [BITVAVO] - root - INFO - Latest MTPI signal is 1
2025-07-11 07:29:14,281 - [BITVAVO] - root - INFO - Latest MTPI signal is bullish (1), will proceed with normal asset selection
2025-07-11 07:29:14,281 - [BITVAVO] - root - INFO - Finished calculating daily scores. DataFrame shape: (211, 14)
2025-07-11 07:29:14,281 - [BITVAVO] - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering ENABLED
2025-07-11 07:29:14,287 - [BITVAVO] - root - INFO - Date ranges for each asset:
2025-07-11 07:29:14,288 - [BITVAVO] - root - INFO -   ETH/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,288 - [BITVAVO] - root - INFO -   BTC/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,288 - [BITVAVO] - root - INFO -   SOL/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,288 - [BITVAVO] - root - INFO -   SUI/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,288 - [BITVAVO] - root - INFO -   XRP/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,288 - [BITVAVO] - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,288 - [BITVAVO] - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,288 - [BITVAVO] - root - INFO -   ADA/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,289 - [BITVAVO] - root - INFO -   LINK/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,289 - [BITVAVO] - root - INFO -   TRX/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,289 - [BITVAVO] - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,289 - [BITVAVO] - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,289 - [BITVAVO] - root - INFO -   BNB/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,289 - [BITVAVO] - root - INFO -   DOT/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,289 - [BITVAVO] - root - INFO - Common dates range: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:29:14,290 - [BITVAVO] - root - INFO - Analysis will run from: 2025-02-10 to 2025-07-10 (151 candles)
2025-07-11 07:29:14,295 - [BITVAVO] - root - INFO - EXECUTION TIMING VERIFICATION:
2025-07-11 07:29:14,295 - [BITVAVO] - root - INFO -    Execution Method: candle_close
2025-07-11 07:29:14,295 - [BITVAVO] - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-07-11 07:29:14,295 - [BITVAVO] - root - INFO -    Signal generated and executed immediately
2025-07-11 07:29:14,306 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-10 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 10.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,307 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,308 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,308 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,308 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,308 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,308 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,308 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,309 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-11 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 9.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-11 07:29:14,309 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,310 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,311 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,311 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,311 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,312 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-12 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 9.0, 'SUI/USDT': 4.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 4.0}
2025-07-11 07:29:14,312 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,312 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,313 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,314 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,314 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,314 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,314 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,315 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-13 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,315 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,315 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,315 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,315 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,316 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,316 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,316 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,316 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,318 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-14 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,318 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,318 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,318 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,318 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,318 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,318 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,318 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,318 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,319 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,319 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,319 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,319 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,319 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,319 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:29:14,319 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,319 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,319 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,319 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,320 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-15 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,320 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,321 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,321 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,321 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,322 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-16 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 5.0}
2025-07-11 07:29:14,322 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,322 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,322 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,322 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,324 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-17 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 7.0}
2025-07-11 07:29:14,324 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,324 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,324 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,324 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,325 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-18 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,326 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,326 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,326 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,326 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,327 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-19 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 5.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,327 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,327 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,327 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,327 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,329 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-20 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-11 07:29:14,329 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,329 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,329 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,329 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,331 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-21 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,331 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,331 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,331 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,331 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,333 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-22 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 13.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,333 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,333 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,333 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,333 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,334 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-23 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-11 07:29:14,334 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,335 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,335 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,335 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,336 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-24 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,336 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,336 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,336 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,336 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,338 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-25 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-11 07:29:14,338 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,338 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,338 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,338 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,339 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-26 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-11 07:29:14,339 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,339 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,339 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,340 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,341 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 5.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 5.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 10.0}
2025-07-11 07:29:14,341 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,341 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,341 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,341 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,343 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-11 07:29:14,343 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,343 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,343 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,343 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,345 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-01 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-11 07:29:14,345 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,345 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,345 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,345 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,346 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-02 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 1.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-11 07:29:14,346 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,346 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,347 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,347 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,348 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-03 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-11 07:29:14,348 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,348 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,348 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,348 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,349 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-04 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,350 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,350 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,350 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,350 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,351 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-05 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,351 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,351 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,351 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,351 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,352 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-06 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-11 07:29:14,353 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,353 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,353 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,353 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,354 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-07 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-11 07:29:14,354 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,354 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,354 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,354 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,356 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-08 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,356 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,356 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,356 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,356 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,357 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-09 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,358 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,358 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,358 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,358 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,359 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-10 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,359 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,360 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,360 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,360 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,361 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-11 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,361 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,361 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,361 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,361 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,362 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-12 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,362 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,362 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,362 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,362 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,363 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-13 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 3.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,364 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,364 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,364 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,364 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,365 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-14 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,365 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,366 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,366 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,366 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,367 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-15 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,367 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,367 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,367 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,367 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,368 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-16 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,369 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,369 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,369 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,369 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,370 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-17 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,370 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,370 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,370 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,370 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,371 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-18 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:29:14,372 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,372 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,372 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,372 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,373 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-19 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-11 07:29:14,373 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,373 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,373 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,374 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,375 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-20 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-11 07:29:14,375 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,375 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,375 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,375 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,376 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-21 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:29:14,376 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,376 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,377 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,377 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,378 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-22 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:29:14,378 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,378 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,378 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,378 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,379 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-23 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:29:14,379 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,380 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,380 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,380 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,381 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-24 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,381 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,381 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,381 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,381 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,382 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-25 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 5.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,383 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,383 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,383 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,383 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,384 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 9.0}
2025-07-11 07:29:14,384 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,385 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,385 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,385 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,386 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 7.0}
2025-07-11 07:29:14,386 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,386 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,386 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,386 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,387 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,387 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,388 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,388 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,388 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,389 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,389 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,389 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,389 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,389 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,390 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,391 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,391 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,391 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,391 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,392 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-31 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,392 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,392 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,392 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,392 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,394 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-01 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,394 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,394 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,394 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,394 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,395 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-02 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,395 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,396 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,396 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,396 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,397 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-03 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-11 07:29:14,397 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,397 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,397 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,397 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,398 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-04 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,398 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,399 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,399 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,399 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,400 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-05 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,400 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,400 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,400 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,400 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,401 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-06 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,402 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,402 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,402 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,402 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,403 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-07 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 11.0, 'DOT/USDT': 7.0}
2025-07-11 07:29:14,403 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,403 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,403 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,404 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,405 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-08 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 5.0}
2025-07-11 07:29:14,405 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,405 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,405 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,405 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,407 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-09 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,407 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,407 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,407 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,407 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,408 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-10 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,409 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,409 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,409 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,409 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,410 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-11 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,410 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,411 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,411 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,411 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,412 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-12 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,413 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,413 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,413 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,413 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,414 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-13 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,415 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,415 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,415 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,416 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,417 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-14 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,417 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,417 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,418 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,418 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,419 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-15 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,419 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,420 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,420 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,420 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,421 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-16 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,421 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,422 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,422 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,422 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,423 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-17 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,423 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,424 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,424 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,424 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,425 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-18 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,425 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,425 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,425 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,425 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,426 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-19 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 4.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,427 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,427 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,427 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,427 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,428 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-20 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 5.0}
2025-07-11 07:29:14,429 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,429 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,429 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,429 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,431 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-21 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,431 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,431 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,432 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,432 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,433 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-22 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 12.0, 'SUI/USDT': 9.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 5.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,434 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,434 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,434 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,434 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,434 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-07-11 07:29:14,434 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-07-11 07:29:14,435 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-07-11 07:29:14,435 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:29:14,435 - [BITVAVO] - root - INFO -    Buying: ['SOL/USDT']
2025-07-11 07:29:14,435 - [BITVAVO] - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-07-11 07:29:14,436 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-23 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,436 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,436 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,436 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,437 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,437 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-07-11 07:29:14,437 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-07-11 07:29:14,437 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-07-11 07:29:14,437 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:29:14,437 - [BITVAVO] - root - INFO -    Selling: ['SOL/USDT']
2025-07-11 07:29:14,437 - [BITVAVO] - root - INFO -    Buying: ['SUI/USDT']
2025-07-11 07:29:14,437 - [BITVAVO] - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-07-11 07:29:14,438 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-24 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,439 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,439 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,439 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,439 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,440 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-25 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-11 07:29:14,440 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,441 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,441 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,441 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,442 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-11 07:29:14,442 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,442 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,442 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,442 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,443 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,444 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,444 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,444 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,444 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,445 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,445 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,445 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,445 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,446 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,447 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,447 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,447 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,447 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,447 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,449 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,449 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,449 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,449 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,449 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,451 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-01 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,451 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,451 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,451 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,451 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,452 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-02 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 8.0, 'SOL/USDT': 10.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,453 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,453 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,453 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,453 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,454 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-03 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,455 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,455 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,455 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,455 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,456 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,457 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,457 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,457 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,457 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,458 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-05 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,459 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,459 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,459 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,459 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,460 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-06 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,461 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,461 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,461 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,461 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,462 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-07 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 0.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,462 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,462 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,462 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,463 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,464 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-08 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 6.0, 'SOL/USDT': 9.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-11 07:29:14,464 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,464 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,464 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,464 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,466 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-09 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 4.0, 'SOL/USDT': 9.0, 'SUI/USDT': 12.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 1.0, 'DOT/USDT': 7.0}
2025-07-11 07:29:14,466 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,466 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,466 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,466 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,466 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-07-11 07:29:14,466 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-07-11 07:29:14,467 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-07-11 07:29:14,467 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:29:14,467 - [BITVAVO] - root - INFO -    Selling: ['SUI/USDT']
2025-07-11 07:29:14,467 - [BITVAVO] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-11 07:29:14,467 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-11 07:29:14,468 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-10 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,468 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,469 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,469 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,469 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,470 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-11 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,470 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,470 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,470 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,471 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,472 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-12 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 11.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,472 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,472 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,472 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,473 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,474 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,474 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,474 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,474 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,474 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,476 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,476 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,476 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,476 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,476 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,477 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,478 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,478 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,478 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,478 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,480 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-16 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,480 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,480 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,480 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,480 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,481 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-17 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,481 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,482 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,482 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,482 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,483 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,483 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,483 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,483 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,483 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,485 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-19 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 5.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,485 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,485 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,485 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,486 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,487 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-20 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:29:14,487 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,487 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,487 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,487 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,487 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-07-11 07:29:14,487 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-07-11 07:29:14,487 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-07-11 07:29:14,487 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:29:14,488 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-11 07:29:14,488 - [BITVAVO] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-11 07:29:14,488 - [BITVAVO] - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-07-11 07:29:14,489 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-21 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,489 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,489 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,489 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,489 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,491 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-22 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,491 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,491 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,491 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,491 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,491 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-07-11 07:29:14,491 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-07-11 07:29:14,491 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-07-11 07:29:14,492 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:29:14,492 - [BITVAVO] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-11 07:29:14,492 - [BITVAVO] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-11 07:29:14,492 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-11 07:29:14,493 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-23 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 7.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 2.0, 'DOT/USDT': 6.0}
2025-07-11 07:29:14,494 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,494 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,494 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,494 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,495 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-24 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 6.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 2.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 4.0}
2025-07-11 07:29:14,496 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,496 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,496 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,496 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,496 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-07-11 07:29:14,496 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-07-11 07:29:14,496 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-07-11 07:29:14,496 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:29:14,496 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-11 07:29:14,496 - [BITVAVO] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-11 07:29:14,497 - [BITVAVO] - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-07-11 07:29:14,498 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-25 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,498 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,498 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,498 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,498 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,500 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-26 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 6.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,500 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,500 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,500 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,500 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,502 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-27 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 7.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,502 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,502 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,502 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,502 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,504 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-28 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 8.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,504 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,504 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,504 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,504 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,505 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-29 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,505 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,505 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,506 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,506 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,507 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-30 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,507 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,507 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,507 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,507 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,508 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-31 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,508 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,509 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,509 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,509 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,511 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-01 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,511 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,511 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,511 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,511 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,512 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-02 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,513 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,513 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,513 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,513 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,514 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-03 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,514 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,514 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,514 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,514 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,515 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-04 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,516 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,516 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,516 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,516 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,517 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-05 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,517 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,517 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,517 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,517 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,518 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-06 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,519 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,519 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,519 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,519 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,520 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-07 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,520 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,520 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,521 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,521 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,522 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-08 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,522 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,522 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,522 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,522 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,523 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,524 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,524 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,524 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,524 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,525 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-10 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,525 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,525 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,526 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,526 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,527 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-11 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,527 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,527 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,527 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,527 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,529 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-12 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,529 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,529 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,529 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,529 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,530 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,531 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,531 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,531 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,531 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,532 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,532 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,532 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,533 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,533 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,534 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,534 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,534 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,534 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,534 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,535 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-16 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,536 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,536 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,536 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,536 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,537 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-17 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-11 07:29:14,537 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,537 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,537 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,538 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,539 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-11 07:29:14,539 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,539 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,539 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,539 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,541 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-19 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,541 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,541 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,541 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,542 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,543 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-20 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-11 07:29:14,543 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,543 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,544 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,544 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:29:14,544 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-07-11 07:29:14,544 - [BITVAVO] - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-07-11 07:29:14,544 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-07-11 07:29:14,544 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:29:14,544 - [BITVAVO] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-11 07:29:14,545 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-21 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-11 07:29:14,546 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,546 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,546 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,546 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,547 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-22 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,548 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,548 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,548 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,548 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,550 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-23 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,550 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,550 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,550 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,550 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,552 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-24 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,552 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,552 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,552 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,552 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,553 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-25 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,554 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,554 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,554 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,554 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,556 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-26 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,556 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,556 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,556 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,556 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,558 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,558 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,558 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,558 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,558 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,560 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,560 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,560 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,560 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,560 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,562 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-29 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,562 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,562 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,562 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,562 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,564 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-30 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,564 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,564 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,564 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,564 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,565 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-01 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,565 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,565 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,566 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,566 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,567 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-02 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:29:14,567 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,567 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,567 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,567 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,568 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-03 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,568 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,569 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,569 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,569 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,570 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,570 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,570 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,570 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,570 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,571 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-05 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 07:29:14,572 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,572 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,572 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,572 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,573 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-06 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 07:29:14,573 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,573 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,574 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,574 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,575 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-07 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 07:29:14,575 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,575 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,575 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,576 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,577 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-08 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-11 07:29:14,577 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,577 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,577 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,577 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,578 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-11 07:29:14,578 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:29:14,578 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:29:14,578 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:29:14,578 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:29:14,646 - [BITVAVO] - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT
2025-07-11 07:29:14,647 - [BITVAVO] - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-07-11 07:29:14,647 - [BITVAVO] - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-07-11 07:29:14,647 - [BITVAVO] - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-11 07:29:14,648 - [BITVAVO] - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-07-11 07:29:14,648 - [BITVAVO] - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-11 07:29:14,648 - [BITVAVO] - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-07-11 07:29:14,649 - [BITVAVO] - root - INFO - Total trades: 7 (Entries: 1, Exits: 1, Swaps: 5)
2025-07-11 07:29:14,651 - [BITVAVO] - root - INFO - Strategy execution completed in 0s
2025-07-11 07:29:14,651 - [BITVAVO] - root - INFO - DEBUG: self.elapsed_time = 0.3697190284729004 seconds
2025-07-11 07:29:14,667 - [BITVAVO] - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-07-11 07:29:14,668 - [BITVAVO] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-11 07:29:14,668 - [BITVAVO] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-11 07:29:14,668 - [BITVAVO] - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-07-11 07:29:14,668 - [BITVAVO] - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-07-11 07:29:14,669 - [BITVAVO] - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-07-11 07:29:14,669 - [BITVAVO] - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-07-11 07:29:14,669 - [BITVAVO] - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-07-11 07:29:14,669 - [BITVAVO] - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-07-11 07:29:14,669 - [BITVAVO] - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-07-11 07:29:14,669 - [BITVAVO] - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-07-11 07:29:14,669 - [BITVAVO] - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-07-11 07:29:14,669 - [BITVAVO] - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-07-11 07:29:14,670 - [BITVAVO] - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-07-11 07:29:14,670 - [BITVAVO] - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-07-11 07:29:14,670 - [BITVAVO] - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-07-11 07:29:14,670 - [BITVAVO] - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-07-11 07:29:14,670 - [BITVAVO] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-11 07:29:14,673 - [BITVAVO] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,676 - [BITVAVO] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,678 - [BITVAVO] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,680 - [BITVAVO] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,683 - [BITVAVO] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,686 - [BITVAVO] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,688 - [BITVAVO] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,690 - [BITVAVO] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,693 - [BITVAVO] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,695 - [BITVAVO] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,697 - [BITVAVO] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,699 - [BITVAVO] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,701 - [BITVAVO] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,703 - [BITVAVO] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:29:14,707 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 211 points
2025-07-11 07:29:14,708 - [BITVAVO] - root - INFO - ETH/USDT B&H total return: 10.90%
2025-07-11 07:29:14,711 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 211 points
2025-07-11 07:29:14,712 - [BITVAVO] - root - INFO - BTC/USDT B&H total return: 19.07%
2025-07-11 07:29:14,716 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 211 points
2025-07-11 07:29:14,716 - [BITVAVO] - root - INFO - SOL/USDT B&H total return: -18.02%
2025-07-11 07:29:14,720 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 211 points
2025-07-11 07:29:14,721 - [BITVAVO] - root - INFO - SUI/USDT B&H total return: 8.55%
2025-07-11 07:29:14,724 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 211 points
2025-07-11 07:29:14,725 - [BITVAVO] - root - INFO - XRP/USDT B&H total return: 5.09%
2025-07-11 07:29:14,728 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 211 points
2025-07-11 07:29:14,728 - [BITVAVO] - root - INFO - AAVE/USDT B&H total return: 22.23%
2025-07-11 07:29:14,732 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 211 points
2025-07-11 07:29:14,733 - [BITVAVO] - root - INFO - AVAX/USDT B&H total return: -19.36%
2025-07-11 07:29:14,736 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 211 points
2025-07-11 07:29:14,736 - [BITVAVO] - root - INFO - ADA/USDT B&H total return: -4.86%
2025-07-11 07:29:14,740 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 211 points
2025-07-11 07:29:14,741 - [BITVAVO] - root - INFO - LINK/USDT B&H total return: -19.03%
2025-07-11 07:29:14,744 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 211 points
2025-07-11 07:29:14,744 - [BITVAVO] - root - INFO - TRX/USDT B&H total return: 19.37%
2025-07-11 07:29:14,748 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 211 points
2025-07-11 07:29:14,748 - [BITVAVO] - root - INFO - PEPE/USDT B&H total return: 29.41%
2025-07-11 07:29:14,752 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 211 points
2025-07-11 07:29:14,752 - [BITVAVO] - root - INFO - DOGE/USDT B&H total return: -23.63%
2025-07-11 07:29:14,756 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 211 points
2025-07-11 07:29:14,757 - [BITVAVO] - root - INFO - BNB/USDT B&H total return: 10.85%
2025-07-11 07:29:14,760 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 211 points
2025-07-11 07:29:14,761 - [BITVAVO] - root - INFO - DOT/USDT B&H total return: -19.90%
2025-07-11 07:29:14,764 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:29:14,778 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:29:14,793 - [BITVAVO] - root - INFO - Using colored segments for single-asset strategy visualization
2025-07-11 07:29:14,941 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:29:14,953 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:29:17,693 - [BITVAVO] - root - INFO - Added ETH/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,693 - [BITVAVO] - root - INFO - Added BTC/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,693 - [BITVAVO] - root - INFO - Added SOL/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,693 - [BITVAVO] - root - INFO - Added SUI/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,693 - [BITVAVO] - root - INFO - Added XRP/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,693 - [BITVAVO] - root - INFO - Added AAVE/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,694 - [BITVAVO] - root - INFO - Added AVAX/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,694 - [BITVAVO] - root - INFO - Added ADA/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,694 - [BITVAVO] - root - INFO - Added LINK/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,694 - [BITVAVO] - root - INFO - Added TRX/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,694 - [BITVAVO] - root - INFO - Added PEPE/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,694 - [BITVAVO] - root - INFO - Added DOGE/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,694 - [BITVAVO] - root - INFO - Added BNB/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,694 - [BITVAVO] - root - INFO - Added DOT/USDT buy-and-hold curve with 211 points
2025-07-11 07:29:17,694 - [BITVAVO] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-11 07:29:17,694 - [BITVAVO] - root - INFO -   - ETH/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,695 - [BITVAVO] - root - INFO -   - BTC/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,695 - [BITVAVO] - root - INFO -   - SOL/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,695 - [BITVAVO] - root - INFO -   - SUI/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,695 - [BITVAVO] - root - INFO -   - XRP/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,695 - [BITVAVO] - root - INFO -   - AAVE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,695 - [BITVAVO] - root - INFO -   - AVAX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,695 - [BITVAVO] - root - INFO -   - ADA/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,695 - [BITVAVO] - root - INFO -   - LINK/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,695 - [BITVAVO] - root - INFO -   - TRX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,696 - [BITVAVO] - root - INFO -   - PEPE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,696 - [BITVAVO] - root - INFO -   - DOGE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,696 - [BITVAVO] - root - INFO -   - BNB/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,696 - [BITVAVO] - root - INFO -   - DOT/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,720 - [BITVAVO] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-11 07:29:17,721 - [BITVAVO] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 07:29:17,723 - [BITVAVO] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-11 07:29:17,723 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:29:17,736 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:29:17,736 - [BITVAVO] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:29:17,737 - [BITVAVO] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:29:17,737 - [BITVAVO] - root - INFO - Combination method: consensus
2025-07-11 07:29:17,737 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-11 07:29:17,737 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-11 07:29:17,764 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:29:17,765 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:29:17,765 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (after filtering).
2025-07-11 07:29:17,765 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 07:29:17,765 - [BITVAVO] - root - INFO - Fetched BTC data: 2152 candles from 2019-08-20 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:29:17,766 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-11 07:29:18,103 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1122)}
2025-07-11 07:29:18,103 - [BITVAVO] - root - INFO - PGO signal: 1
2025-07-11 07:29:18,103 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-11 07:29:18,210 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1126)}
2025-07-11 07:29:18,211 - [BITVAVO] - root - INFO - Bollinger Bands signal: 1
2025-07-11 07:29:23,992 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-11 07:29:23,992 - [BITVAVO] - root - INFO - DWMA Score signal: 1
2025-07-11 07:29:24,655 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-11 07:29:24,655 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(883)}
2025-07-11 07:29:24,656 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-11 07:29:24,656 - [BITVAVO] - root - INFO - DEMA Super Score signal: 1
2025-07-11 07:29:26,342 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-11 07:29:26,342 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(978)}
2025-07-11 07:29:26,342 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-11 07:29:26,342 - [BITVAVO] - root - INFO - DPSD Score signal: 1
2025-07-11 07:29:26,437 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-11 07:29:26,437 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-11 07:29:26,437 - [BITVAVO] - root - INFO - AAD Score signal: 1
2025-07-11 07:29:27,300 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-11 07:29:27,300 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-11 07:29:27,301 - [BITVAVO] - root - INFO - Dynamic EMA Score signal: 1
2025-07-11 07:29:28,772 - [BITVAVO] - root - INFO - Quantile DEMA Score signal: 1
2025-07-11 07:29:28,773 - [BITVAVO] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-11 07:29:28,773 - [BITVAVO] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-11 07:29:28,773 - [BITVAVO] - root - INFO - MTPI Score: 1.000000
2025-07-11 07:29:28,773 - [BITVAVO] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-11 07:29:28,777 - [BITVAVO] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-11 07:29:28,777 - [BITVAVO] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 07:29:28,777 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 07:29:28,777 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 4.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 11.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 10.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 10.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 1.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 8.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-11 07:29:28,778 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 6.0)
2025-07-11 07:29:28,779 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-11 07:29:28,779 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 0.0)
2025-07-11 07:29:28,779 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:29:28,779 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:29:28,779 - [BITVAVO] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:29:28,787 - [BITVAVO] - root - INFO - Saved metrics to new file: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250704_run_20250711_072852.csv
2025-07-11 07:29:28,787 - [BITVAVO] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250704_run_20250711_072852.csv
2025-07-11 07:29:28,787 - [BITVAVO] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-11 07:29:28,787 - [BITVAVO] - root - INFO - Results type: <class 'dict'>
2025-07-11 07:29:28,787 - [BITVAVO] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-11 07:29:28,787 - [BITVAVO] - root - INFO - Success flag set to: True
2025-07-11 07:29:28,787 - [BITVAVO] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-11 07:29:28,787 - [BITVAVO] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 271 entries
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 211 entries
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO -   - metrics_file: <class 'str'>
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO -   - success: <class 'bool'>
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO -   - message: <class 'str'>
2025-07-11 07:29:28,788 - [BITVAVO] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-11 07:29:28,788 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: 
2025-07-11 07:29:28,789 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-06 00:00:00+00:00    
2025-07-07 00:00:00+00:00    
2025-07-08 00:00:00+00:00    
2025-07-09 00:00:00+00:00    
2025-07-10 00:00:00+00:00    
dtype: object
2025-07-11 07:29:28,790 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:29:28,790 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:29:28,790 - [BITVAVO] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-07-11 07:29:28,790 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-11 07:29:28,790 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:29:28,790 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:29:28,790 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 11.0
2025-07-11 07:29:28,790 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 11.0: ['SUI/EUR']
2025-07-11 07:29:28,790 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-11 07:29:28,790 - [BITVAVO] - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 11.0)
2025-07-11 07:29:28,790 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-11 07:29:28,790 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 11.0
2025-07-11 07:29:28,790 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-11 07:29:28,791 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION:  -> SUI/EUR
2025-07-11 07:29:28,791 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-11 07:29:28,827 - [BITVAVO] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-11 07:29:28,828 - [BITVAVO] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:29:28,828 - [BITVAVO] - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-11 07:29:28,828 - [BITVAVO] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-11 07:29:28,828 - [BITVAVO] - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-11 07:29:28,828 - [BITVAVO] - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-11 07:29:28,828 - [BITVAVO] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-11 07:29:28,828 - [BITVAVO] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-11 07:29:28,828 - [BITVAVO] - root - ERROR - 🚨 ? SUI/EUR WAS SELECTED
2025-07-11 07:29:28,828 - [BITVAVO] - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-11 07:29:28,828 - [BITVAVO] - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=live
2025-07-11 07:29:28,992 - [BITVAVO] - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-11 07:29:28,993 - [BITVAVO] - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: SUI/EUR
2025-07-11 07:29:28,993 - [BITVAVO] - root - INFO - Attempting to enter position for SUI/EUR in live mode
2025-07-11 07:29:28,993 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Starting enter_position attempt
2025-07-11 07:29:28,993 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Trading mode: live
2025-07-11 07:29:28,993 - [BITVAVO] - root - INFO - TRADE ATTEMPT - SUI/EUR: Getting current market price...
2025-07-11 07:29:28,993 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-11 07:29:28,993 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-11 07:29:28,993 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Initializing exchange bitvavo
2025-07-11 07:29:29,005 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange initialized successfully
2025-07-11 07:29:29,006 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange markets not loaded, loading now...
2025-07-11 07:29:29,393 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found after loading markets
2025-07-11 07:29:29,394 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-11 07:29:29,445 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-11 07:29:29,446 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1752218967327, 'datetime': '2025-07-11T07:29:27.327Z', 'high': 3.0319, 'low': 2.7271, 'bid': 2.9733, 'bidVolume': 741.841, 'ask': 2.975, 'askVolume': 335.78703237, 'vwap': 2.878196707090034, 'open': 2.755, 'close': 2.9789, 'last': 2.9789, 'previousClose': None, 'change': 0.2239, 'percentage': 8.127041742286751, 'average': 2.86695, 'baseVolume': 2846628.67956969, 'quoteVolume': 8193157.291845534, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1752132567327', 'timestamp': '1752218967327', 'open': '2.755', 'openTimestamp': '1752132572843', 'high': '3.0319', 'low': '2.7271', 'last': '2.9789', 'closeTimestamp': '1752218941150', 'bid': '2.973300', 'bidSize': '741.84100000', 'ask': '2.975000', 'askSize': '335.78703237', 'volume': '2846628.67956969', 'volumeQuote': '8193157.29184553406'}, 'indexPrice': None, 'markPrice': None}
2025-07-11 07:29:29,446 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 2.9789
2025-07-11 07:29:29,447 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: get_current_price returned: 2.9789
2025-07-11 07:29:29,447 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price type: <class 'float'>
2025-07-11 07:29:29,448 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - not price: False
2025-07-11 07:29:29,448 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - price <= 0: False
2025-07-11 07:29:29,448 - [BITVAVO] - root - INFO - TRADE ATTEMPT - SUI/EUR: Current price: 2.********
2025-07-11 07:29:29,520 - [BITVAVO] - root - INFO - Available balance for EUR: 4544.********
2025-07-11 07:29:29,528 - [BITVAVO] - root - INFO - Loaded market info for 176 trading pairs
2025-07-11 07:29:29,529 - [BITVAVO] - root - INFO - Calculated position size for SUI/EUR: 15.******** (using 1% of 4544.82, accounting for fees)
2025-07-11 07:29:29,530 - [BITVAVO] - root - INFO - Calculated position size: 15.******** SUI
2025-07-11 07:29:29,530 - [BITVAVO] - root - INFO - Entering position for SUI/EUR: 15.******** units at 2.******** (value: 45.******** EUR)
2025-07-11 07:29:29,530 - [BITVAVO] - root - INFO - Executing live market buy order for SUI/EUR
2025-07-11 07:29:29,615 - [BITVAVO] - root - INFO - Adjusted base amount for SUI/EUR: 15.******** -> 15.********
2025-07-11 07:29:29,729 - [BITVAVO] - root - INFO - Created market buy order: SUI/EUR, amount: 15.***************, avg price: 2.9751
2025-07-11 07:29:29,730 - [BITVAVO] - root - INFO - Order fee: 0.********4265 EUR
2025-07-11 07:29:29,730 - [BITVAVO] - root - INFO - Filled amount: 15.********
2025-07-11 07:29:29,730 - [BITVAVO] - root - INFO - Filled amount: 15.******** SUI
2025-07-11 07:29:29,730 - [BITVAVO] - root - INFO - Order fee: 0.******** EUR
2025-07-11 07:29:29,730 - [BITVAVO] - root - INFO - Successfully entered position: SUI/EUR, amount: 15.********, price: 2.97510000
2025-07-11 07:29:29,734 - [BITVAVO] - root - INFO - Trade executed: BUY SUI/EUR, amount=15.********, price=2.97510000, filled=15.********
2025-07-11 07:29:29,734 - [BITVAVO] - root - INFO -   Fee: 0.******** EUR
2025-07-11 07:29:29,734 - [BITVAVO] - root - INFO - TRADE SUCCESS - SUI/EUR: Successfully updated current asset
2025-07-11 07:29:29,736 - [BITVAVO] - root - INFO - Trade executed: BUY SUI/EUR, amount=15.********, price=2.97510000, filled=15.********
2025-07-11 07:29:29,736 - [BITVAVO] - root - INFO -   Fee: 0.******** EUR
2025-07-11 07:29:29,736 - [BITVAVO] - root - INFO - Single-asset trade result logged to trade log file
2025-07-11 07:29:29,736 - [BITVAVO] - root - INFO - Trade executed: {'success': True, 'symbol': 'SUI/EUR', 'side': 'buy', 'amount': 15.***************, 'price': 2.9751, 'order': {'info': {'orderId': '00000000-0000-056e-0100-00019cbbc50a', 'market': 'SUI-EUR', 'created': '1752218969711', 'updated': '1752218969711', 'status': 'filled', 'side': 'buy', 'orderType': 'market', 'selfTradePrevention': 'decrementAndCancel', 'visible': False, 'onHold': '0', 'onHoldCurrency': 'EUR', 'fills': [{'id': '00000000-0000-056e-0000-00000036fb70', 'timestamp': '1752218969711', 'amount': '15.********', 'price': '2.9751', 'taker': True, 'fee': '0.********4265', 'feeCurrency': 'EUR', 'settled': True}], 'feePaid': '0.********4265', 'feeCurrency': 'EUR', 'operatorId': '1001', 'disableMarketProtection': False, 'amount': '15.********', 'amountRemaining': '0', 'filledAmount': '15.********', 'filledAmountQuote': '44.937457005735'}, 'id': '00000000-0000-056e-0100-00019cbbc50a', 'clientOrderId': None, 'timestamp': 1752218969711, 'datetime': '2025-07-11T07:29:29.711Z', 'lastTradeTimestamp': 1752218969711, 'symbol': 'SUI/EUR', 'type': 'market', 'timeInForce': 'IOC', 'postOnly': None, 'side': 'buy', 'price': 2.9751, 'triggerPrice': None, 'amount': 15.********, 'cost': 44.937457005735, 'average': 2.9751, 'filled': 15.********, 'remaining': 0.0, 'status': 'closed', 'fee': {'cost': 0.********4265, 'currency': 'EUR'}, 'trades': [{'info': {'id': '00000000-0000-056e-0000-00000036fb70', 'timestamp': '1752218969711', 'amount': '15.********', 'price': '2.9751', 'taker': True, 'fee': '0.********4265', 'feeCurrency': 'EUR', 'settled': True}, 'id': '00000000-0000-056e-0000-00000036fb70', 'symbol': 'SUI/EUR', 'timestamp': 1752218969711, 'datetime': '2025-07-11T07:29:29.711Z', 'order': None, 'type': None, 'side': None, 'takerOrMaker': 'taker', 'price': 2.9751, 'amount': 15.********, 'cost': 44.937457005735, 'fee': {'currency': 'EUR', 'cost': 0.********4265}, 'fees': [{'currency': 'EUR', 'cost': 0.********4265}]}], 'fees': [{'currency': 'EUR', 'cost': 0.********4265}], 'lastUpdateTimestamp': None, 'reduceOnly': None, 'stopPrice': None, 'takeProfitPrice': None, 'stopLossPrice': None, 'average_price': 2.9751}, 'filled_amount': 15.********, 'fee': {'cost': 0.********4265, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'SUI', 'timestamp': '2025-07-11T07:29:29.730445'}
2025-07-11 07:29:29,808 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO - Asset scores (sorted by score):
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO -   SUI/EUR: score=11.0, status=SELECTED, weight=1.00
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO -   XRP/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO -   AAVE/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO -   LINK/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO -   DOGE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO -   SOL/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,813 - [BITVAVO] - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,814 - [BITVAVO] - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,814 - [BITVAVO] - root - INFO -   AVAX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,814 - [BITVAVO] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,814 - [BITVAVO] - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,814 - [BITVAVO] - root - INFO -   DOT/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:29:29,814 - [BITVAVO] - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-11 07:29:29,814 - [BITVAVO] - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:29:29,864 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:29:29,865 - [BITVAVO] - root - INFO - Strategy execution completed successfully in 37.31 seconds
2025-07-11 07:29:29,868 - [BITVAVO] - root - INFO - Saved recovery state to data/state/recovery_state.json
