2025-07-11 07:25:00,411 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Exchange-specific logging initialized for bitvavo
2025-07-11 07:25:00,733 - [B<PERSON><PERSON><PERSON>] - root - INFO - Telegram command handlers registered
2025-07-11 07:25:00,735 - [<PERSON><PERSON><PERSON><PERSON>] - root - ERROR - Error in Telegram polling: set_wakeup_fd only works in main thread of the main interpreter
2025-07-11 07:25:00,735 - [BITVAVO] - root - WARNING - Continuing without Telegram bot polling. Notifications will still be sent.
2025-07-11 07:25:00,735 - [BITVAVO] - root - INFO - Telegram bot polling started
2025-07-11 07:25:00,735 - [BITVAVO] - root - INFO - Telegram notifier initialized with notification level: standard
2025-07-11 07:25:00,735 - [B<PERSON><PERSON><PERSON>] - root - INFO - Telegram notification channel initialized
2025-07-11 07:25:00,736 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Successfully loaded templates using utf-8 encoding
2025-07-11 07:25:00,736 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - <PERSON>aded 24 templates from file
2025-07-11 07:25:00,736 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Notification manager initialized with 1 channels
2025-07-11 07:25:00,736 - [BITVAVO] - root - INFO - Notification manager initialized
2025-07-11 07:25:00,736 - [BITVAVO] - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-07-11 07:25:00,736 - [BITVAVO] - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-07-11 07:25:00,736 - [BITVAVO] - root - INFO - Set up critical time windows for 1d timeframe
2025-07-11 07:25:00,736 - [BITVAVO] - root - INFO - Network watchdog initialized with 10s check interval
2025-07-11 07:25:00,738 - [BITVAVO] - root - INFO - Loaded recovery state from data/state/recovery_state.json
2025-07-11 07:25:00,738 - [BITVAVO] - root - INFO - No state file found at /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/data/state/data/state/background_service_20250627_214259.json.json
2025-07-11 07:25:00,738 - [BITVAVO] - root - INFO - Recovery manager initialized
2025-07-11 07:25:00,738 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-07-11 07:25:00,738 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'exchange_params': {'bitvavo': {'operator_id': 'asset_rotation_strategy_v1'}}, 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'live', 'order_type': 'market', 'position_size_pct': 1, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-07-11 07:25:00,738 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:25:00,753 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:00,753 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:25:00,753 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:25:00,753 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:25:00,754 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:25:00,754 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:25:00,754 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:25:00,754 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:25:00,754 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:25:00,754 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:25:00,766 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:01,187 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-07-11 07:25:01,308 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-11 07:25:01,312 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:25:01,313 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:25:01,313 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:25:01,313 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:25:01,313 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:25:01,330 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:01,881 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-11 07:25:01,881 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:25:01,895 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:01,895 - [BITVAVO] - root - INFO - Trading executor initialized for bitvavo
2025-07-11 07:25:01,895 - [BITVAVO] - root - INFO - Trading mode: live
2025-07-11 07:25:01,895 - [BITVAVO] - root - INFO - Trading enabled: True
2025-07-11 07:25:01,895 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:25:01,895 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:25:01,896 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:25:01,896 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:25:01,896 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:25:01,911 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:02,368 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-07-11 07:25:02,488 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-11 07:25:02,488 - [BITVAVO] - root - INFO - Trading enabled in live mode
2025-07-11 07:25:02,783 - [BITVAVO] - root - INFO - Connected to bitvavo, balance: 4544.82 EUR
2025-07-11 07:25:02,783 - [BITVAVO] - root - INFO - Generated run ID: 20250711_072502
2025-07-11 07:25:02,783 - [BITVAVO] - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-07-11 07:25:02,783 - [BITVAVO] - root - INFO - Background service initialized
2025-07-11 07:25:02,784 - [BITVAVO] - root - INFO - Network watchdog started
2025-07-11 07:25:02,784 - [BITVAVO] - root - INFO - Network watchdog started
2025-07-11 07:25:02,785 - [BITVAVO] - root - INFO - Schedule set up for 1d timeframe
2025-07-11 07:25:02,785 - [BITVAVO] - root - INFO - Background service started
2025-07-11 07:25:02,786 - [BITVAVO] - root - INFO - Executing strategy (run #1)...
2025-07-11 07:25:02,791 - [BITVAVO] - root - INFO - Resetting daily trade counters for this strategy execution
2025-07-11 07:25:02,792 - [BITVAVO] - root - INFO - No trades recorded today (Max: 5)
2025-07-11 07:25:02,792 - [BITVAVO] - root - INFO - Initialized daily trades counter for 2025-07-11
2025-07-11 07:25:02,793 - [BITVAVO] - root - INFO - Creating snapshot for candle timestamp: 20250711
2025-07-11 07:25:02,875 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:25:03,805 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Unknown error in HTTP implementation: RuntimeError('<asyncio.locks.Event object at 0x75a34d5605c0 [unset]> is bound to a different event loop')
2025-07-11 07:25:04,042 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:25:04,043 - [BITVAVO] - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-07-11 07:25:04,044 - [BITVAVO] - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-07-11 07:25:04,044 - [BITVAVO] - root - INFO - Using recent date for performance tracking: 2025-07-04
2025-07-11 07:25:04,045 - [BITVAVO] - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-07-11 07:25:04,106 - [BITVAVO] - root - INFO - Loaded 2152 rows of ETH/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,106 - [BITVAVO] - root - INFO - Last timestamp in cache for ETH/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,107 - [BITVAVO] - root - INFO - Expected last timestamp for ETH/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,107 - [BITVAVO] - root - INFO - Data is up to date for ETH/USDT
2025-07-11 07:25:04,109 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,137 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,137 - [BITVAVO] - root - INFO - Last timestamp in cache for BTC/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,138 - [BITVAVO] - root - INFO - Expected last timestamp for BTC/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,138 - [BITVAVO] - root - INFO - Data is up to date for BTC/USDT
2025-07-11 07:25:04,139 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,163 - [BITVAVO] - root - INFO - Loaded 1795 rows of SOL/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,163 - [BITVAVO] - root - INFO - Last timestamp in cache for SOL/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,164 - [BITVAVO] - root - INFO - Expected last timestamp for SOL/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,164 - [BITVAVO] - root - INFO - Data is up to date for SOL/USDT
2025-07-11 07:25:04,165 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,179 - [BITVAVO] - root - INFO - Loaded 800 rows of SUI/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,180 - [BITVAVO] - root - INFO - Last timestamp in cache for SUI/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,180 - [BITVAVO] - root - INFO - Expected last timestamp for SUI/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,180 - [BITVAVO] - root - INFO - Data is up to date for SUI/USDT
2025-07-11 07:25:04,181 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,208 - [BITVAVO] - root - INFO - Loaded 2152 rows of XRP/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,208 - [BITVAVO] - root - INFO - Last timestamp in cache for XRP/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,209 - [BITVAVO] - root - INFO - Expected last timestamp for XRP/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,209 - [BITVAVO] - root - INFO - Data is up to date for XRP/USDT
2025-07-11 07:25:04,211 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,234 - [BITVAVO] - root - INFO - Loaded 1730 rows of AAVE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,234 - [BITVAVO] - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,235 - [BITVAVO] - root - INFO - Expected last timestamp for AAVE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,235 - [BITVAVO] - root - INFO - Data is up to date for AAVE/USDT
2025-07-11 07:25:04,236 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,263 - [BITVAVO] - root - INFO - Loaded 1753 rows of AVAX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,263 - [BITVAVO] - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,264 - [BITVAVO] - root - INFO - Expected last timestamp for AVAX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,264 - [BITVAVO] - root - INFO - Data is up to date for AVAX/USDT
2025-07-11 07:25:04,265 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,293 - [BITVAVO] - root - INFO - Loaded 2152 rows of ADA/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,293 - [BITVAVO] - root - INFO - Last timestamp in cache for ADA/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,294 - [BITVAVO] - root - INFO - Expected last timestamp for ADA/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,294 - [BITVAVO] - root - INFO - Data is up to date for ADA/USDT
2025-07-11 07:25:04,296 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,321 - [BITVAVO] - root - INFO - Loaded 2152 rows of LINK/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,322 - [BITVAVO] - root - INFO - Last timestamp in cache for LINK/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,322 - [BITVAVO] - root - INFO - Expected last timestamp for LINK/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,322 - [BITVAVO] - root - INFO - Data is up to date for LINK/USDT
2025-07-11 07:25:04,323 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,351 - [BITVAVO] - root - INFO - Loaded 2152 rows of TRX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,351 - [BITVAVO] - root - INFO - Last timestamp in cache for TRX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,352 - [BITVAVO] - root - INFO - Expected last timestamp for TRX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,352 - [BITVAVO] - root - INFO - Data is up to date for TRX/USDT
2025-07-11 07:25:04,353 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,367 - [BITVAVO] - root - INFO - Loaded 798 rows of PEPE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,367 - [BITVAVO] - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,368 - [BITVAVO] - root - INFO - Expected last timestamp for PEPE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,368 - [BITVAVO] - root - INFO - Data is up to date for PEPE/USDT
2025-07-11 07:25:04,369 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,396 - [BITVAVO] - root - INFO - Loaded 2152 rows of DOGE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,398 - [BITVAVO] - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,398 - [BITVAVO] - root - INFO - Expected last timestamp for DOGE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,399 - [BITVAVO] - root - INFO - Data is up to date for DOGE/USDT
2025-07-11 07:25:04,400 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,422 - [BITVAVO] - root - INFO - Loaded 2152 rows of BNB/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,422 - [BITVAVO] - root - INFO - Last timestamp in cache for BNB/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,423 - [BITVAVO] - root - INFO - Expected last timestamp for BNB/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,423 - [BITVAVO] - root - INFO - Data is up to date for BNB/USDT
2025-07-11 07:25:04,424 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,443 - [BITVAVO] - root - INFO - Loaded 1788 rows of DOT/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,444 - [BITVAVO] - root - INFO - Last timestamp in cache for DOT/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,444 - [BITVAVO] - root - INFO - Expected last timestamp for DOT/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:25:04,444 - [BITVAVO] - root - INFO - Data is up to date for DOT/USDT
2025-07-11 07:25:04,445 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,446 - [BITVAVO] - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-07-11 07:25:04,447 - [BITVAVO] - root - INFO - MTPI Multi-Indicator Configuration:
2025-07-11 07:25:04,447 - [BITVAVO] - root - INFO -   - Number of indicators: 8
2025-07-11 07:25:04,447 - [BITVAVO] - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:25:04,447 - [BITVAVO] - root - INFO -   - Combination method: consensus
2025-07-11 07:25:04,447 - [BITVAVO] - root - INFO -   - Long threshold: 0.1
2025-07-11 07:25:04,447 - [BITVAVO] - root - INFO -   - Short threshold: -0.1
2025-07-11 07:25:04,447 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 07:25:04,447 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:25:04,447 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-07-11 07:25:04,447 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-07-11 07:25:04,447 - [BITVAVO] - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-07-11 07:25:04,447 - [BITVAVO] - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-07-11 07:25:04,447 - [BITVAVO] - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-07-11 07:25:04,447 - [BITVAVO] - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-07-11 07:25:04,448 - [BITVAVO] - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-07-11 07:25:04,448 - [BITVAVO] - root - INFO - n_assets=1, use_weighted_allocation=False
2025-07-11 07:25:04,448 - [BITVAVO] - root - INFO - Using provided trend method: PGO For Loop
2025-07-11 07:25:04,448 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:25:04,461 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:04,461 - [BITVAVO] - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-07-11 07:25:04,469 - [BITVAVO] - root - INFO - Configuration saved successfully.
2025-07-11 07:25:04,470 - [BITVAVO] - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 07:25:04,470 - [BITVAVO] - root - INFO - Number of trend detection assets: 14
2025-07-11 07:25:04,470 - [BITVAVO] - root - INFO - Selected assets type: <class 'list'>
2025-07-11 07:25:04,470 - [BITVAVO] - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:25:04,470 - [BITVAVO] - root - INFO - Number of trading assets: 14
2025-07-11 07:25:04,470 - [BITVAVO] - root - INFO - Trading assets type: <class 'list'>
2025-07-11 07:25:04,831 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:25:04,848 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:04,862 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:25:04,875 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:04,875 - [BITVAVO] - root - INFO - Execution context: backtesting
2025-07-11 07:25:04,875 - [BITVAVO] - root - INFO - Execution timing: candle_close
2025-07-11 07:25:04,875 - [BITVAVO] - root - INFO - Ratio calculation method: independent
2025-07-11 07:25:04,875 - [BITVAVO] - root - INFO - Tie-breaking strategy: imcumbent
2025-07-11 07:25:04,875 - [BITVAVO] - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-07-11 07:25:04,876 - [BITVAVO] - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:25:04,876 - [BITVAVO] - root - INFO - MTPI combination method override: consensus
2025-07-11 07:25:04,876 - [BITVAVO] - root - INFO - MTPI long threshold override: 0.1
2025-07-11 07:25:04,876 - [BITVAVO] - root - INFO - MTPI short threshold override: -0.1
2025-07-11 07:25:04,876 - [BITVAVO] - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-07-11 07:25:04,876 - [BITVAVO] - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-11 07:25:04,878 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,878 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,878 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,879 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,879 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,879 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,879 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,879 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,879 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,880 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,880 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,880 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,880 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,880 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:25:04,880 - [BITVAVO] - root - INFO - Checking cache for 14 symbols (1d)...
2025-07-11 07:25:04,903 - [BITVAVO] - root - INFO - Loaded 211 rows of ETH/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,904 - [BITVAVO] - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:04,904 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,905 - [BITVAVO] - root - INFO - Loaded 211 rows of ETH/USDT data from cache (after filtering).
2025-07-11 07:25:04,925 - [BITVAVO] - root - INFO - Loaded 211 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,926 - [BITVAVO] - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:04,926 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,927 - [BITVAVO] - root - INFO - Loaded 211 rows of BTC/USDT data from cache (after filtering).
2025-07-11 07:25:04,945 - [BITVAVO] - root - INFO - Loaded 211 rows of SOL/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,946 - [BITVAVO] - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:04,947 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,947 - [BITVAVO] - root - INFO - Loaded 211 rows of SOL/USDT data from cache (after filtering).
2025-07-11 07:25:04,957 - [BITVAVO] - root - INFO - Loaded 211 rows of SUI/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,958 - [BITVAVO] - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:04,958 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,959 - [BITVAVO] - root - INFO - Loaded 211 rows of SUI/USDT data from cache (after filtering).
2025-07-11 07:25:04,979 - [BITVAVO] - root - INFO - Loaded 211 rows of XRP/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,980 - [BITVAVO] - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:04,981 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,981 - [BITVAVO] - root - INFO - Loaded 211 rows of XRP/USDT data from cache (after filtering).
2025-07-11 07:25:04,998 - [BITVAVO] - root - INFO - Loaded 211 rows of AAVE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:04,999 - [BITVAVO] - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:04,999 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:04,999 - [BITVAVO] - root - INFO - Loaded 211 rows of AAVE/USDT data from cache (after filtering).
2025-07-11 07:25:05,017 - [BITVAVO] - root - INFO - Loaded 211 rows of AVAX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:05,018 - [BITVAVO] - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:05,019 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:05,019 - [BITVAVO] - root - INFO - Loaded 211 rows of AVAX/USDT data from cache (after filtering).
2025-07-11 07:25:05,038 - [BITVAVO] - root - INFO - Loaded 211 rows of ADA/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:05,039 - [BITVAVO] - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:05,040 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:05,040 - [BITVAVO] - root - INFO - Loaded 211 rows of ADA/USDT data from cache (after filtering).
2025-07-11 07:25:05,067 - [BITVAVO] - root - INFO - Loaded 211 rows of LINK/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:05,068 - [BITVAVO] - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:05,068 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:05,068 - [BITVAVO] - root - INFO - Loaded 211 rows of LINK/USDT data from cache (after filtering).
2025-07-11 07:25:05,093 - [BITVAVO] - root - INFO - Loaded 211 rows of TRX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:05,094 - [BITVAVO] - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:05,094 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:05,094 - [BITVAVO] - root - INFO - Loaded 211 rows of TRX/USDT data from cache (after filtering).
2025-07-11 07:25:05,105 - [BITVAVO] - root - INFO - Loaded 211 rows of PEPE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:05,106 - [BITVAVO] - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:05,106 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:05,106 - [BITVAVO] - root - INFO - Loaded 211 rows of PEPE/USDT data from cache (after filtering).
2025-07-11 07:25:05,127 - [BITVAVO] - root - INFO - Loaded 211 rows of DOGE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:05,128 - [BITVAVO] - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:05,129 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:05,129 - [BITVAVO] - root - INFO - Loaded 211 rows of DOGE/USDT data from cache (after filtering).
2025-07-11 07:25:05,149 - [BITVAVO] - root - INFO - Loaded 211 rows of BNB/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:05,150 - [BITVAVO] - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:05,150 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:05,150 - [BITVAVO] - root - INFO - Loaded 211 rows of BNB/USDT data from cache (after filtering).
2025-07-11 07:25:05,167 - [BITVAVO] - root - INFO - Loaded 211 rows of DOT/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:05,168 - [BITVAVO] - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:25:05,168 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:05,168 - [BITVAVO] - root - INFO - Loaded 211 rows of DOT/USDT data from cache (after filtering).
2025-07-11 07:25:05,169 - [BITVAVO] - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 07:25:05,169 - [BITVAVO] - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,169 - [BITVAVO] - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,169 - [BITVAVO] - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,169 - [BITVAVO] - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,169 - [BITVAVO] - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,170 - [BITVAVO] - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,170 - [BITVAVO] - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,170 - [BITVAVO] - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,170 - [BITVAVO] - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,170 - [BITVAVO] - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,170 - [BITVAVO] - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,170 - [BITVAVO] - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,171 - [BITVAVO] - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,171 - [BITVAVO] - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:25:05,210 - [BITVAVO] - root - INFO - Using standard MTPI warmup period of 120 days
2025-07-11 07:25:05,211 - [BITVAVO] - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-11 07:25:05,211 - [BITVAVO] - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-07-11 07:25:05,211 - [BITVAVO] - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-11 07:25:05,211 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:25:05,223 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:05,224 - [BITVAVO] - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-07-11 07:25:05,224 - [BITVAVO] - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-11 07:25:05,224 - [BITVAVO] - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:25:05,224 - [BITVAVO] - root - INFO - Override: combination_method = consensus
2025-07-11 07:25:05,224 - [BITVAVO] - root - INFO - Override: long_threshold = 0.1
2025-07-11 07:25:05,224 - [BITVAVO] - root - INFO - Override: short_threshold = -0.1
2025-07-11 07:25:05,224 - [BITVAVO] - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-07-11 07:25:05,224 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-11 07:25:05,224 - [BITVAVO] - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-07-11 07:25:05,224 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-11 07:25:05,247 - [BITVAVO] - root - INFO - Loaded 271 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:05,247 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:05,248 - [BITVAVO] - root - INFO - Loaded 271 rows of BTC/USDT data from cache (after filtering).
2025-07-11 07:25:05,248 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 07:25:05,248 - [BITVAVO] - root - INFO - Fetched BTC data: 271 candles from 2024-10-13 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:05,248 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-11 07:25:05,298 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(111), 0: np.int64(34), 1: np.int64(126)}
2025-07-11 07:25:05,298 - [BITVAVO] - root - INFO - Generated pgo signals: 271 values
2025-07-11 07:25:05,298 - [BITVAVO] - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-07-11 07:25:05,298 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-11 07:25:05,316 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(109), 0: np.int64(32), 1: np.int64(130)}
2025-07-11 07:25:05,317 - [BITVAVO] - root - INFO - Generated Bollinger Band signals: 271 values
2025-07-11 07:25:05,317 - [BITVAVO] - root - INFO - Generated bollinger_bands signals: 271 values
2025-07-11 07:25:05,955 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-11 07:25:05,955 - [BITVAVO] - root - INFO - Generated dwma_score signals: 271 values
2025-07-11 07:25:06,036 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-11 07:25:06,036 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(156), 0: np.int64(1), 1: np.int64(114)}
2025-07-11 07:25:06,036 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-11 07:25:06,036 - [BITVAVO] - root - INFO - Generated dema_super_score signals: 271 values
2025-07-11 07:25:06,218 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-11 07:25:06,219 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(105), 0: np.int64(87), 1: np.int64(79)}
2025-07-11 07:25:06,219 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-11 07:25:06,219 - [BITVAVO] - root - INFO - Generated dpsd_score signals: 271 values
2025-07-11 07:25:06,234 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-11 07:25:06,235 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-11 07:25:06,235 - [BITVAVO] - root - INFO - Generated aad_score signals: 271 values
2025-07-11 07:25:06,339 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-11 07:25:06,339 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-11 07:25:06,340 - [BITVAVO] - root - INFO - Generated dynamic_ema_score signals: 271 values
2025-07-11 07:25:06,521 - [BITVAVO] - root - INFO - Generated quantile_dema_score signals: 271 values
2025-07-11 07:25:06,533 - [BITVAVO] - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-07-11 07:25:06,534 - [BITVAVO] - root - INFO - Signal distribution: {1: 146, -1: 124, 0: 1}
2025-07-11 07:25:06,534 - [BITVAVO] - root - INFO - Generated combined MTPI signals: 271 values using consensus method
2025-07-11 07:25:06,535 - [BITVAVO] - root - INFO - Signal distribution: {1: 146, -1: 124, 0: 1}
2025-07-11 07:25:06,535 - [BITVAVO] - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-07-11 07:25:06,538 - [BITVAVO] - root - INFO - Saving configuration to config/settings.yaml...
2025-07-11 07:25:06,545 - [BITVAVO] - root - INFO - Configuration saved successfully.
2025-07-11 07:25:06,545 - [BITVAVO] - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-07-11 07:25:06,545 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:25:06,557 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:06,558 - [BITVAVO] - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-07-11 07:25:06,558 - [BITVAVO] - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-07-11 07:25:06,558 - [BITVAVO] - root - INFO - Using ratio calculation method: independent
2025-07-11 07:25:06,596 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:06,644 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:06,681 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:06,681 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:06,717 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:06,727 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:06,761 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:25:06,761 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:06,792 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:25:06,801 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:06,835 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:06,835 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:06,870 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:06,879 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:06,916 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:25:06,916 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:06,946 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:25:06,955 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:06,994 - [BITVAVO] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-11 07:25:06,994 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,027 - [BITVAVO] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-11 07:25:07,037 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:07,074 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:25:07,074 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,106 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:25:07,115 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:07,150 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:25:07,151 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,181 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:25:07,191 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:07,225 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:25:07,226 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,262 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:25:07,276 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:07,320 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:25:07,320 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,381 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:25:07,398 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:07,435 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:07,436 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,466 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:07,476 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:07,516 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,570 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:07,607 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:25:07,607 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,637 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:25:07,646 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:07,680 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:25:07,680 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,710 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:25:07,719 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:07,751 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,787 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:07,819 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:25:07,819 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,848 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:25:07,857 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:07,891 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:25:07,891 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,921 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:25:07,931 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:07,962 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:25:07,962 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:07,991 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:25:08,000 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:08,037 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:25:08,038 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,068 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:25:08,078 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:08,114 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:08,115 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,155 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:08,164 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:08,199 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:25:08,199 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,231 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:25:08,240 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:08,276 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:08,277 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,309 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:08,319 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:08,357 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:25:08,358 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,390 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:25:08,401 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:08,434 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,475 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:08,516 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,557 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:08,599 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:25:08,599 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,638 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:25:08,648 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:08,679 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-11 07:25:08,679 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,708 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-11 07:25:08,718 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:08,751 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,788 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:08,826 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:08,826 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,854 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:08,864 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:08,897 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:08,898 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:08,933 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:08,942 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:08,974 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-11 07:25:08,974 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,003 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-11 07:25:09,014 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:09,050 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:25:09,050 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,082 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:25:09,092 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:09,126 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:25:09,126 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,154 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:25:09,163 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:09,197 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:25:09,197 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,227 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:25:09,236 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:09,270 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:25:09,270 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,303 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:25:09,313 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:09,347 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:25:09,347 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,375 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:25:09,385 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:09,416 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:09,417 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,445 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:09,455 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:09,487 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:09,488 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,521 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:09,530 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:09,563 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:09,563 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,593 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:09,602 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:09,634 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:09,635 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,665 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:09,675 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:09,711 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,753 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:09,786 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,829 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:09,862 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,900 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:09,941 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:25:09,941 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:09,972 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:25:09,981 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:10,015 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,057 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:10,091 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,129 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:10,161 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:10,161 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,191 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:10,200 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:10,235 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,276 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:10,311 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,349 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:10,379 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:10,380 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,407 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:10,421 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:10,463 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,503 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:10,539 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,580 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:10,614 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:25:10,614 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,645 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:25:10,656 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:10,691 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,729 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:10,762 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,800 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:10,832 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:10,832 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,862 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:10,872 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:10,903 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:10,903 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:10,932 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:10,943 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:10,977 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:25:10,978 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,008 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:25:11,019 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:11,055 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:25:11,055 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,083 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:25:11,093 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:11,125 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:11,125 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,153 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:11,163 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:11,195 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:11,195 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,222 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:11,231 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:11,263 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:11,263 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,292 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:25:11,301 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:11,333 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,374 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:11,407 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,446 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:11,478 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:25:11,478 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,510 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:25:11,520 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:11,552 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,591 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:11,624 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,658 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:11,688 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,725 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:11,757 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,794 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:11,828 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,865 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:11,896 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:11,896 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,926 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:11,935 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:11,967 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:11,967 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:11,996 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:12,007 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:12,043 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:25:12,043 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,072 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:25:12,081 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:12,114 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,152 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:12,187 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,223 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:12,254 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:25:12,255 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,281 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:25:12,293 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:12,328 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,368 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:12,401 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:12,401 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,430 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:12,442 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:12,481 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,531 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:12,572 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,624 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:12,667 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,706 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:12,741 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,781 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:12,815 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,854 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:12,887 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:25:12,887 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,915 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:25:12,924 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:12,954 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:12,995 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:13,029 - [BITVAVO] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-11 07:25:13,029 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,060 - [BITVAVO] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-11 07:25:13,070 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:13,106 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,154 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:13,188 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,227 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:13,260 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,301 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:13,334 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,373 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:13,410 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,465 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:13,498 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:13,498 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,533 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:13,542 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:13,574 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,633 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:13,678 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,723 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:13,756 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:25:13,756 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,786 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:25:13,798 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:13,835 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,876 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:13,909 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:25:13,909 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:13,941 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:25:13,950 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:13,993 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:25:13,993 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,026 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:25:14,036 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:14,069 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:25:14,070 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,098 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:25:14,107 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:14,139 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,180 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:14,212 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:14,212 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,245 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:14,255 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:14,288 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:14,288 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,320 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:14,330 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:14,366 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,409 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:14,441 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:14,441 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,470 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:14,479 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:14,512 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,553 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:14,589 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,628 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:14,662 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,709 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:14,743 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,785 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:14,816 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,853 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:14,886 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:25:14,886 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:14,918 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:25:14,928 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:14,964 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,000 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:15,035 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,079 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:15,115 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,151 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:15,183 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:15,184 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,212 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:15,223 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:15,257 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,299 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:15,338 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,380 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:15,413 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,454 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:15,487 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,528 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:15,563 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,609 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:15,643 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:15,643 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,675 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:15,684 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:15,723 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,771 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:15,823 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:15,823 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,867 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:15,878 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:15,912 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:15,912 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:15,940 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:15,953 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:15,987 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:25:15,987 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,013 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:25:16,023 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:16,056 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,097 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:16,127 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:16,128 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,156 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:16,166 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:16,200 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:16,200 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,229 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:16,242 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:16,277 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,317 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:16,350 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,388 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:16,421 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,461 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:16,505 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,548 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:16,582 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,620 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:16,652 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,689 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:16,721 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,758 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:16,792 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,832 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:16,865 - [BITVAVO] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-11 07:25:16,866 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,896 - [BITVAVO] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-11 07:25:16,906 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:16,937 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:25:16,937 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:16,966 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:25:16,976 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:17,011 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:17,011 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,042 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:25:17,052 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:17,086 - [BITVAVO] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-11 07:25:17,087 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,120 - [BITVAVO] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-11 07:25:17,130 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:17,164 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,206 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:17,238 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,280 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:17,314 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,354 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:17,389 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,431 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:17,463 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,501 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:17,533 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,572 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:17,605 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,643 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:17,675 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:25:17,676 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,706 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:25:17,717 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:17,756 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:17,757 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,788 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:25:17,798 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:17,830 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:17,830 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,864 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:17,875 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:17,911 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:25:17,911 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:17,941 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:25:17,951 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:17,986 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:25:17,986 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,016 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:25:18,026 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:18,059 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:18,059 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,088 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:18,098 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:18,130 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:25:18,130 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,160 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:25:18,169 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:18,207 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:25:18,207 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,244 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:25:18,258 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:18,300 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,349 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:18,387 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:18,388 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,418 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:18,429 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:18,462 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,502 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:18,537 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,575 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:18,608 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:18,609 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,641 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:18,651 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:18,685 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:18,685 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,718 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:18,730 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:18,768 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:25:18,769 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,802 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:25:18,816 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:18,854 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:25:18,855 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,886 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:25:18,897 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:18,933 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:25:18,934 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:18,971 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:25:18,980 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:19,012 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:19,013 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,048 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:19,058 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:19,092 - [BITVAVO] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-11 07:25:19,092 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,123 - [BITVAVO] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-11 07:25:19,135 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:19,169 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:19,169 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,197 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:25:19,208 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:19,243 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:19,243 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,278 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:19,287 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:19,323 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,365 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:19,402 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,445 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:25:19,495 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,536 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:25:19,568 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,606 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:25:19,642 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,682 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:25:19,718 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:19,718 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,748 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:25:19,757 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:25:19,790 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:25:19,791 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,828 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:25:19,838 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:25:19,870 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:25:19,871 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:19,900 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:25:19,910 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:25:19,956 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:25:19,957 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:20,001 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:25:20,017 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:25:20,074 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-11 07:25:20,074 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:20,126 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-11 07:25:20,142 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:25:20,198 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:25:20,199 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:20,246 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:25:20,260 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:25:20,305 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:20,346 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:25:20,383 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:20,384 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:20,417 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:25:20,426 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:25:20,463 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:20,506 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:25:20,540 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:25:20,582 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:25:24,500 - [BITVAVO] - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-07-11 07:25:24,500 - [BITVAVO] - root - INFO - Latest MTPI signal is 1
2025-07-11 07:25:24,500 - [BITVAVO] - root - INFO - Latest MTPI signal is bullish (1), will proceed with normal asset selection
2025-07-11 07:25:24,501 - [BITVAVO] - root - INFO - Finished calculating daily scores. DataFrame shape: (211, 14)
2025-07-11 07:25:24,501 - [BITVAVO] - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering ENABLED
2025-07-11 07:25:24,507 - [BITVAVO] - root - INFO - Date ranges for each asset:
2025-07-11 07:25:24,508 - [BITVAVO] - root - INFO -   ETH/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,508 - [BITVAVO] - root - INFO -   BTC/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,508 - [BITVAVO] - root - INFO -   SOL/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,508 - [BITVAVO] - root - INFO -   SUI/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,508 - [BITVAVO] - root - INFO -   XRP/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,508 - [BITVAVO] - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,509 - [BITVAVO] - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,509 - [BITVAVO] - root - INFO -   ADA/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,509 - [BITVAVO] - root - INFO -   LINK/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,509 - [BITVAVO] - root - INFO -   TRX/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,509 - [BITVAVO] - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,509 - [BITVAVO] - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,509 - [BITVAVO] - root - INFO -   BNB/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,509 - [BITVAVO] - root - INFO -   DOT/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,510 - [BITVAVO] - root - INFO - Common dates range: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:25:24,510 - [BITVAVO] - root - INFO - Analysis will run from: 2025-02-10 to 2025-07-10 (151 candles)
2025-07-11 07:25:24,516 - [BITVAVO] - root - INFO - EXECUTION TIMING VERIFICATION:
2025-07-11 07:25:24,516 - [BITVAVO] - root - INFO -    Execution Method: candle_close
2025-07-11 07:25:24,516 - [BITVAVO] - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-07-11 07:25:24,516 - [BITVAVO] - root - INFO -    Signal generated and executed immediately
2025-07-11 07:25:24,528 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-10 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 10.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,528 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,528 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,528 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,528 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,528 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,528 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,528 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,529 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,529 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,529 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,529 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,529 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,529 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,529 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,529 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,529 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,529 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,529 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,530 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-11 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 9.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-11 07:25:24,531 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,531 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,531 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,531 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,531 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,531 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,531 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,531 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,531 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,531 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,532 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,532 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,532 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,532 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,532 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,532 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,532 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,532 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,533 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-12 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 9.0, 'SUI/USDT': 4.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 4.0}
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,534 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,535 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,535 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,535 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,535 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,536 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-13 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,536 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,536 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,536 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,536 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,536 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,537 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,537 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,537 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,537 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,537 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,537 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,537 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,537 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,537 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,537 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,537 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,537 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,537 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,539 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-14 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,540 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,540 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,540 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,540 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,540 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,540 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,540 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,540 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,540 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,541 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,541 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,541 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,541 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,541 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:25:24,541 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,541 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,541 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,541 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,542 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-15 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,542 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,543 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,543 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,543 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,544 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-16 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 5.0}
2025-07-11 07:25:24,544 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,544 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,544 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,544 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,546 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-17 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 7.0}
2025-07-11 07:25:24,546 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,546 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,546 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,546 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,547 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-18 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,547 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,547 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,548 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,548 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,549 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-19 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 5.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,549 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,549 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,549 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,549 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,550 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-20 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-11 07:25:24,551 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,551 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,551 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,551 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,552 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-21 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,552 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,552 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,552 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,552 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,554 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-22 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 13.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,554 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,554 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,554 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,554 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,555 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-23 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-11 07:25:24,556 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,556 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,556 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,556 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,557 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-24 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,557 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,557 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,557 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,557 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,558 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-25 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-11 07:25:24,559 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,559 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,559 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,559 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,560 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-26 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-11 07:25:24,560 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,560 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,560 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,560 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,562 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 5.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 5.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 10.0}
2025-07-11 07:25:24,562 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,562 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,562 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,562 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,563 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-11 07:25:24,563 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,564 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,564 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,564 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,565 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-01 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-11 07:25:24,565 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,565 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,565 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,565 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,567 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-02 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 1.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-11 07:25:24,567 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,567 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,567 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,567 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,568 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-03 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-11 07:25:24,568 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,568 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,569 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,569 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,570 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-04 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,570 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,570 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,570 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,570 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,571 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-05 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,571 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,572 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,572 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,572 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,573 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-06 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-11 07:25:24,573 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,573 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,573 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,573 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,574 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-07 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-11 07:25:24,574 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,574 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,575 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,575 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,576 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-08 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,576 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,576 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,576 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,576 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,577 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-09 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,577 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,577 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,577 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,577 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,578 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-10 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,579 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,579 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,579 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,579 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,580 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-11 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,580 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,580 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,580 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,580 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,581 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-12 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,582 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,582 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,582 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,582 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,583 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-13 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 3.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,583 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,583 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,583 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,583 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,584 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-14 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,585 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,585 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,585 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,585 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,586 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-15 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,586 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,586 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,586 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,586 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,587 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-16 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,588 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,588 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,588 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,588 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,590 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-17 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,590 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,590 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,590 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,590 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,592 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-18 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:25:24,592 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,592 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,592 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,592 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,594 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-19 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-11 07:25:24,594 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,594 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,594 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,594 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,595 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-20 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-11 07:25:24,596 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,596 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,596 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,596 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,597 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-21 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:25:24,598 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,598 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,598 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,598 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,599 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-22 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:25:24,599 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,600 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,600 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,600 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,601 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-23 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:25:24,601 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,601 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,602 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,602 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,603 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-24 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,603 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,603 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,604 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,604 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,605 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-25 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 5.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,605 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,605 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,605 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,605 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,607 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 9.0}
2025-07-11 07:25:24,607 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,607 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,607 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,607 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,609 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 7.0}
2025-07-11 07:25:24,609 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,609 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,609 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,609 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,611 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,611 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,611 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,611 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,611 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,612 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,613 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,613 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,613 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,613 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,614 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,614 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,615 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,615 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,615 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,616 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-31 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,616 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,616 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,616 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,616 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,618 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-01 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,618 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,618 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,618 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,618 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,619 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-02 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,619 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,619 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,620 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,620 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,621 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-03 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-11 07:25:24,621 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,621 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,621 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,621 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,623 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-04 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,623 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,624 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,624 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,624 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,626 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-05 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,626 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,626 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,626 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,626 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,628 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-06 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,630 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,630 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,630 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,631 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,632 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-07 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 11.0, 'DOT/USDT': 7.0}
2025-07-11 07:25:24,633 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,633 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,633 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,633 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,635 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-08 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 5.0}
2025-07-11 07:25:24,635 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,636 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,636 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,636 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,638 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-09 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,638 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,638 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,638 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,638 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,640 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-10 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,640 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,640 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,640 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,640 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,641 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-11 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,642 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,642 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,642 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,642 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,643 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-12 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,643 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,644 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,644 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,644 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,645 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-13 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,645 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,645 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,645 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,646 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,647 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-14 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,647 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,647 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,647 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,647 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,648 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-15 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,649 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,649 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,649 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,649 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,650 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-16 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,650 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,650 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,650 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,650 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,652 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-17 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,652 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,652 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,652 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,652 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,653 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-18 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,654 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,654 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,654 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,654 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,655 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-19 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 4.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,655 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,655 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,655 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,655 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,657 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-20 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 5.0}
2025-07-11 07:25:24,657 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,657 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,657 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,657 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,659 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-21 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,659 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,659 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,659 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,659 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,661 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-22 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 12.0, 'SUI/USDT': 9.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 5.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,661 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,661 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,661 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,661 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,662 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-07-11 07:25:24,662 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-07-11 07:25:24,662 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-07-11 07:25:24,662 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:25:24,662 - [BITVAVO] - root - INFO -    Buying: ['SOL/USDT']
2025-07-11 07:25:24,662 - [BITVAVO] - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-07-11 07:25:24,664 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-23 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,664 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,664 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,664 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,665 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,665 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-07-11 07:25:24,665 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-07-11 07:25:24,665 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-07-11 07:25:24,665 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:25:24,665 - [BITVAVO] - root - INFO -    Selling: ['SOL/USDT']
2025-07-11 07:25:24,666 - [BITVAVO] - root - INFO -    Buying: ['SUI/USDT']
2025-07-11 07:25:24,666 - [BITVAVO] - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-07-11 07:25:24,668 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-24 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,668 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,669 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,669 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,669 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,671 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-25 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-11 07:25:24,671 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,672 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,672 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,672 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,674 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-11 07:25:24,674 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,675 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,675 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,675 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,677 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,677 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,677 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,677 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,677 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,679 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,679 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,679 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,679 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,680 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,681 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,682 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,682 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,682 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,682 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,683 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,684 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,684 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,684 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,684 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,686 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-01 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,686 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,686 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,686 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,686 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,688 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-02 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 8.0, 'SOL/USDT': 10.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,688 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,689 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,689 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,689 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,690 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-03 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,690 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,690 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,690 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,691 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,692 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,693 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,693 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,693 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,693 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,694 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-05 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,694 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,695 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,695 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,695 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,696 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-06 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,696 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,696 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,697 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,697 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,699 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-07 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 0.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,699 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,699 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,699 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,699 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,701 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-08 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 6.0, 'SOL/USDT': 9.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-11 07:25:24,701 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,702 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,702 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,702 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,704 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-09 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 4.0, 'SOL/USDT': 9.0, 'SUI/USDT': 12.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 1.0, 'DOT/USDT': 7.0}
2025-07-11 07:25:24,704 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,704 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,705 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,705 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,705 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-07-11 07:25:24,705 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-07-11 07:25:24,705 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-07-11 07:25:24,705 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:25:24,705 - [BITVAVO] - root - INFO -    Selling: ['SUI/USDT']
2025-07-11 07:25:24,705 - [BITVAVO] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-11 07:25:24,706 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-11 07:25:24,708 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-10 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,708 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,708 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,709 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,710 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,711 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-11 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,712 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,712 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,712 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,712 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,713 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-12 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 11.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,713 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,713 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,714 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,714 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,715 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,715 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,715 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,716 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,716 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,717 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,717 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,717 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,717 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,718 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,719 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,719 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,719 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,719 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,720 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,721 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-16 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,721 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,721 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,721 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,721 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,723 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-17 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,723 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,723 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,723 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,723 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,724 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,724 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,725 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,725 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,725 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,726 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-19 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 5.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,726 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,726 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,726 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,726 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,727 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-20 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:25:24,728 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,728 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,728 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,728 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,728 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-07-11 07:25:24,728 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-07-11 07:25:24,728 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-07-11 07:25:24,728 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:25:24,728 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-11 07:25:24,728 - [BITVAVO] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-11 07:25:24,729 - [BITVAVO] - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-07-11 07:25:24,730 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-21 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,730 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,730 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,730 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,730 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,731 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-22 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,731 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,732 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,732 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,732 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,732 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-07-11 07:25:24,732 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-07-11 07:25:24,732 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-07-11 07:25:24,732 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:25:24,732 - [BITVAVO] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-11 07:25:24,732 - [BITVAVO] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-11 07:25:24,732 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-11 07:25:24,733 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-23 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 7.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 2.0, 'DOT/USDT': 6.0}
2025-07-11 07:25:24,734 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,734 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,734 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,734 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,735 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-24 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 6.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 2.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 4.0}
2025-07-11 07:25:24,735 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,736 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,736 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,736 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,736 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-07-11 07:25:24,736 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-07-11 07:25:24,736 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-07-11 07:25:24,736 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:25:24,736 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-11 07:25:24,736 - [BITVAVO] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-11 07:25:24,736 - [BITVAVO] - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-07-11 07:25:24,738 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-25 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,738 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,738 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,738 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,738 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,739 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-26 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 6.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,740 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,740 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,740 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,740 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,741 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-27 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 7.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,741 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,741 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,741 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,742 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,743 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-28 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 8.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,743 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,743 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,743 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,743 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,744 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-29 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,745 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,745 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,745 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,745 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,746 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-30 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,746 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,746 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,746 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,747 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,748 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-31 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,748 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,748 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,748 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,748 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,749 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-01 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,750 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,750 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,750 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,750 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,751 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-02 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,751 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,751 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,751 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,751 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,753 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-03 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,753 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,753 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,753 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,753 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,754 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-04 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,754 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,755 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,755 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,755 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,756 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-05 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,756 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,757 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,757 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,757 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,758 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-06 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,758 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,758 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,758 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,758 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,760 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-07 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,760 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,760 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,760 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,760 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,762 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-08 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,762 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,762 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,762 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,762 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,764 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,764 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,764 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,764 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,764 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,766 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-10 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,766 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,766 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,766 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,766 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,768 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-11 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,768 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,768 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,768 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,768 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,770 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-12 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,770 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,770 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,770 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,770 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,772 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,772 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,772 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,772 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,773 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,774 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,774 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,774 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,774 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,775 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,776 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,776 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,776 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,776 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,776 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,777 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-16 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,777 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,778 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,778 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,778 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,779 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-17 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-11 07:25:24,779 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,779 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,780 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,780 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,781 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-11 07:25:24,781 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,781 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,781 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,781 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,783 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-19 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,783 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,783 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,783 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,783 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,785 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-20 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-11 07:25:24,785 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,785 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,785 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,785 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:25:24,785 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-07-11 07:25:24,785 - [BITVAVO] - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-07-11 07:25:24,785 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-07-11 07:25:24,786 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:25:24,786 - [BITVAVO] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-11 07:25:24,787 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-21 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-11 07:25:24,787 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,787 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,787 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,787 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,789 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-22 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,790 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,790 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,790 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,790 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,792 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-23 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,793 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,793 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,793 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,793 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,795 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-24 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,796 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,796 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,796 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,796 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,798 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-25 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,798 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,798 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,798 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,799 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,800 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-26 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,800 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,800 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,800 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,800 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,802 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,802 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,802 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,802 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,803 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,804 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,804 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,805 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,805 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,805 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,806 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-29 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,806 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,807 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,807 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,807 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,808 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-30 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,809 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,809 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,809 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,809 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,810 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-01 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,810 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,810 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,810 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,810 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,811 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-02 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:25:24,812 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,812 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,812 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,812 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,813 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-03 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,813 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,813 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,813 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,814 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,815 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,815 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,815 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,815 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,815 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,817 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-05 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 07:25:24,817 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,817 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,817 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,818 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,819 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-06 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 07:25:24,819 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,819 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,819 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,819 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,821 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-07 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 07:25:24,821 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,821 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,822 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,822 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,824 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-08 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-11 07:25:24,824 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,824 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,824 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,825 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,827 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-11 07:25:24,827 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:25:24,827 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:25:24,827 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:25:24,827 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:25:24,924 - [BITVAVO] - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT
2025-07-11 07:25:24,925 - [BITVAVO] - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-07-11 07:25:24,925 - [BITVAVO] - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-07-11 07:25:24,926 - [BITVAVO] - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-11 07:25:24,926 - [BITVAVO] - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-07-11 07:25:24,926 - [BITVAVO] - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-11 07:25:24,926 - [BITVAVO] - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-07-11 07:25:24,927 - [BITVAVO] - root - INFO - Total trades: 7 (Entries: 1, Exits: 1, Swaps: 5)
2025-07-11 07:25:24,930 - [BITVAVO] - root - INFO - Strategy execution completed in 0s
2025-07-11 07:25:24,930 - [BITVAVO] - root - INFO - DEBUG: self.elapsed_time = 0.42884302139282227 seconds
2025-07-11 07:25:24,952 - [BITVAVO] - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-07-11 07:25:24,953 - [BITVAVO] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-11 07:25:24,953 - [BITVAVO] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-11 07:25:24,953 - [BITVAVO] - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-07-11 07:25:24,953 - [BITVAVO] - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-07-11 07:25:24,954 - [BITVAVO] - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-07-11 07:25:24,954 - [BITVAVO] - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-07-11 07:25:24,954 - [BITVAVO] - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-07-11 07:25:24,954 - [BITVAVO] - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-07-11 07:25:24,954 - [BITVAVO] - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-07-11 07:25:24,954 - [BITVAVO] - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-07-11 07:25:24,954 - [BITVAVO] - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-07-11 07:25:24,954 - [BITVAVO] - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-07-11 07:25:24,955 - [BITVAVO] - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-07-11 07:25:24,955 - [BITVAVO] - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-07-11 07:25:24,955 - [BITVAVO] - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-07-11 07:25:24,955 - [BITVAVO] - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-07-11 07:25:24,955 - [BITVAVO] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-11 07:25:24,959 - [BITVAVO] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,962 - [BITVAVO] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,965 - [BITVAVO] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,968 - [BITVAVO] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,970 - [BITVAVO] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,973 - [BITVAVO] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,976 - [BITVAVO] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,979 - [BITVAVO] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,982 - [BITVAVO] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,984 - [BITVAVO] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,987 - [BITVAVO] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,990 - [BITVAVO] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,993 - [BITVAVO] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:24,996 - [BITVAVO] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:25:25,001 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 211 points
2025-07-11 07:25:25,002 - [BITVAVO] - root - INFO - ETH/USDT B&H total return: 10.90%
2025-07-11 07:25:25,006 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 211 points
2025-07-11 07:25:25,007 - [BITVAVO] - root - INFO - BTC/USDT B&H total return: 19.07%
2025-07-11 07:25:25,011 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 211 points
2025-07-11 07:25:25,011 - [BITVAVO] - root - INFO - SOL/USDT B&H total return: -18.02%
2025-07-11 07:25:25,016 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 211 points
2025-07-11 07:25:25,017 - [BITVAVO] - root - INFO - SUI/USDT B&H total return: 8.55%
2025-07-11 07:25:25,021 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 211 points
2025-07-11 07:25:25,022 - [BITVAVO] - root - INFO - XRP/USDT B&H total return: 5.09%
2025-07-11 07:25:25,026 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 211 points
2025-07-11 07:25:25,026 - [BITVAVO] - root - INFO - AAVE/USDT B&H total return: 22.23%
2025-07-11 07:25:25,030 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 211 points
2025-07-11 07:25:25,031 - [BITVAVO] - root - INFO - AVAX/USDT B&H total return: -19.36%
2025-07-11 07:25:25,035 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 211 points
2025-07-11 07:25:25,036 - [BITVAVO] - root - INFO - ADA/USDT B&H total return: -4.86%
2025-07-11 07:25:25,040 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 211 points
2025-07-11 07:25:25,040 - [BITVAVO] - root - INFO - LINK/USDT B&H total return: -19.03%
2025-07-11 07:25:25,044 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 211 points
2025-07-11 07:25:25,045 - [BITVAVO] - root - INFO - TRX/USDT B&H total return: 19.37%
2025-07-11 07:25:25,049 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 211 points
2025-07-11 07:25:25,049 - [BITVAVO] - root - INFO - PEPE/USDT B&H total return: 29.41%
2025-07-11 07:25:25,054 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 211 points
2025-07-11 07:25:25,054 - [BITVAVO] - root - INFO - DOGE/USDT B&H total return: -23.63%
2025-07-11 07:25:25,058 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 211 points
2025-07-11 07:25:25,059 - [BITVAVO] - root - INFO - BNB/USDT B&H total return: 10.85%
2025-07-11 07:25:25,063 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 211 points
2025-07-11 07:25:25,064 - [BITVAVO] - root - INFO - DOT/USDT B&H total return: -19.90%
2025-07-11 07:25:25,069 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:25:25,089 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:25,110 - [BITVAVO] - root - INFO - Using colored segments for single-asset strategy visualization
2025-07-11 07:25:25,308 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:25:25,323 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:28,170 - [BITVAVO] - root - INFO - Added ETH/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,171 - [BITVAVO] - root - INFO - Added BTC/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,171 - [BITVAVO] - root - INFO - Added SOL/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,171 - [BITVAVO] - root - INFO - Added SUI/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,171 - [BITVAVO] - root - INFO - Added XRP/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,171 - [BITVAVO] - root - INFO - Added AAVE/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,171 - [BITVAVO] - root - INFO - Added AVAX/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,171 - [BITVAVO] - root - INFO - Added ADA/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,171 - [BITVAVO] - root - INFO - Added LINK/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,171 - [BITVAVO] - root - INFO - Added TRX/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,171 - [BITVAVO] - root - INFO - Added PEPE/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,172 - [BITVAVO] - root - INFO - Added DOGE/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,172 - [BITVAVO] - root - INFO - Added BNB/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,172 - [BITVAVO] - root - INFO - Added DOT/USDT buy-and-hold curve with 211 points
2025-07-11 07:25:28,172 - [BITVAVO] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-11 07:25:28,172 - [BITVAVO] - root - INFO -   - ETH/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,172 - [BITVAVO] - root - INFO -   - BTC/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,172 - [BITVAVO] - root - INFO -   - SOL/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,172 - [BITVAVO] - root - INFO -   - SUI/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,173 - [BITVAVO] - root - INFO -   - XRP/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,173 - [BITVAVO] - root - INFO -   - AAVE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,173 - [BITVAVO] - root - INFO -   - AVAX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,173 - [BITVAVO] - root - INFO -   - ADA/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,173 - [BITVAVO] - root - INFO -   - LINK/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,173 - [BITVAVO] - root - INFO -   - TRX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,173 - [BITVAVO] - root - INFO -   - PEPE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,173 - [BITVAVO] - root - INFO -   - DOGE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,174 - [BITVAVO] - root - INFO -   - BNB/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,174 - [BITVAVO] - root - INFO -   - DOT/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,198 - [BITVAVO] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-11 07:25:28,198 - [BITVAVO] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 07:25:28,201 - [BITVAVO] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-11 07:25:28,201 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:25:28,213 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:25:28,214 - [BITVAVO] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:25:28,214 - [BITVAVO] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:25:28,214 - [BITVAVO] - root - INFO - Combination method: consensus
2025-07-11 07:25:28,214 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-11 07:25:28,214 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-11 07:25:28,231 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:25:28,232 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:25:28,232 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (after filtering).
2025-07-11 07:25:28,233 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 07:25:28,233 - [BITVAVO] - root - INFO - Fetched BTC data: 2152 candles from 2019-08-20 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:25:28,233 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-11 07:25:28,605 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1122)}
2025-07-11 07:25:28,606 - [BITVAVO] - root - INFO - PGO signal: 1
2025-07-11 07:25:28,606 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-11 07:25:28,723 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1126)}
2025-07-11 07:25:28,723 - [BITVAVO] - root - INFO - Bollinger Bands signal: 1
2025-07-11 07:25:34,292 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-11 07:25:34,292 - [BITVAVO] - root - INFO - DWMA Score signal: 1
2025-07-11 07:25:34,896 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-11 07:25:34,896 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(883)}
2025-07-11 07:25:34,896 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-11 07:25:34,897 - [BITVAVO] - root - INFO - DEMA Super Score signal: 1
2025-07-11 07:25:36,497 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-11 07:25:36,497 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(978)}
2025-07-11 07:25:36,497 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-11 07:25:36,497 - [BITVAVO] - root - INFO - DPSD Score signal: 1
2025-07-11 07:25:36,609 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-11 07:25:36,609 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-11 07:25:36,609 - [BITVAVO] - root - INFO - AAD Score signal: 1
2025-07-11 07:25:37,417 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-11 07:25:37,418 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-11 07:25:37,418 - [BITVAVO] - root - INFO - Dynamic EMA Score signal: 1
2025-07-11 07:25:38,839 - [BITVAVO] - root - INFO - Quantile DEMA Score signal: 1
2025-07-11 07:25:38,839 - [BITVAVO] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-11 07:25:38,839 - [BITVAVO] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-11 07:25:38,839 - [BITVAVO] - root - INFO - MTPI Score: 1.000000
2025-07-11 07:25:38,839 - [BITVAVO] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-11 07:25:38,843 - [BITVAVO] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-11 07:25:38,843 - [BITVAVO] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 07:25:38,843 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 07:25:38,843 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 07:25:38,843 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 07:25:38,843 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 4.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 11.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 10.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 10.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 1.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 8.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 6.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 0.0)
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:25:38,844 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:25:38,845 - [BITVAVO] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:25:38,850 - [BITVAVO] - root - INFO - Saved metrics to new file: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250704_run_20250711_072502.csv
2025-07-11 07:25:38,850 - [BITVAVO] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250704_run_20250711_072502.csv
2025-07-11 07:25:38,850 - [BITVAVO] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO - Results type: <class 'dict'>
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO - Success flag set to: True
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 271 entries
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 211 entries
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-11 07:25:38,851 - [BITVAVO] - root - INFO -   - metrics_file: <class 'str'>
2025-07-11 07:25:38,852 - [BITVAVO] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-11 07:25:38,852 - [BITVAVO] - root - INFO -   - success: <class 'bool'>
2025-07-11 07:25:38,852 - [BITVAVO] - root - INFO -   - message: <class 'str'>
2025-07-11 07:25:38,852 - [BITVAVO] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-11 07:25:38,852 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: 
2025-07-11 07:25:38,853 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-06 00:00:00+00:00    
2025-07-07 00:00:00+00:00    
2025-07-08 00:00:00+00:00    
2025-07-09 00:00:00+00:00    
2025-07-10 00:00:00+00:00    
dtype: object
2025-07-11 07:25:38,853 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:25:38,853 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:25:38,854 - [BITVAVO] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-07-11 07:25:38,854 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-11 07:25:38,854 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:25:38,854 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:25:38,854 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 11.0
2025-07-11 07:25:38,854 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 11.0: ['SUI/EUR']
2025-07-11 07:25:38,854 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-11 07:25:38,854 - [BITVAVO] - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 11.0)
2025-07-11 07:25:38,854 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-11 07:25:38,854 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 11.0
2025-07-11 07:25:38,854 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-11 07:25:38,854 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION:  -> SUI/EUR
2025-07-11 07:25:38,854 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-11 07:25:38,883 - [BITVAVO] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-11 07:25:38,883 - [BITVAVO] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:25:38,884 - [BITVAVO] - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-11 07:25:38,884 - [BITVAVO] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-11 07:25:38,884 - [BITVAVO] - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-11 07:25:38,884 - [BITVAVO] - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-11 07:25:38,884 - [BITVAVO] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-11 07:25:38,884 - [BITVAVO] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-11 07:25:38,884 - [BITVAVO] - root - ERROR - 🚨 ? SUI/EUR WAS SELECTED
2025-07-11 07:25:38,884 - [BITVAVO] - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-11 07:25:38,884 - [BITVAVO] - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=live
2025-07-11 07:25:39,043 - [BITVAVO] - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-11 07:25:39,043 - [BITVAVO] - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: SUI/EUR
2025-07-11 07:25:39,043 - [BITVAVO] - root - INFO - Attempting to enter position for SUI/EUR in live mode
2025-07-11 07:25:39,043 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Starting enter_position attempt
2025-07-11 07:25:39,044 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Trading mode: live
2025-07-11 07:25:39,044 - [BITVAVO] - root - INFO - TRADE ATTEMPT - SUI/EUR: Getting current market price...
2025-07-11 07:25:39,044 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-11 07:25:39,044 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-11 07:25:39,044 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Initializing exchange bitvavo
2025-07-11 07:25:39,056 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange initialized successfully
2025-07-11 07:25:39,057 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange markets not loaded, loading now...
2025-07-11 07:25:39,445 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found after loading markets
2025-07-11 07:25:39,446 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-11 07:25:39,496 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-11 07:25:39,496 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1752218737256, 'datetime': '2025-07-11T07:25:37.256Z', 'high': 3.0319, 'low': 2.7271, 'bid': 2.9702, 'bidVolume': 428.0862739, 'ask': 2.9727, 'askVolume': 231.49391551, 'vwap': 2.8769078552474228, 'open': 2.7504, 'close': 2.9707, 'last': 2.9707, 'previousClose': None, 'change': 0.2203, 'percentage': 8.009744037230949, 'average': 2.86055, 'baseVolume': 2868610.83548612, 'quoteVolume': 8252729.046257891, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1752132337256', 'timestamp': '1752218737256', 'open': '2.7504', 'openTimestamp': '1752132347352', 'high': '3.0319', 'low': '2.7271', 'last': '2.9707', 'closeTimestamp': '1752218689350', 'bid': '2.970200', 'bidSize': '428.08627390', 'ask': '2.972700', 'askSize': '231.49391551', 'volume': '2868610.83548612', 'volumeQuote': '8252729.046257890446'}, 'indexPrice': None, 'markPrice': None}
2025-07-11 07:25:39,497 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 2.9707
2025-07-11 07:25:39,497 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: get_current_price returned: 2.9707
2025-07-11 07:25:39,497 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price type: <class 'float'>
2025-07-11 07:25:39,497 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - not price: False
2025-07-11 07:25:39,497 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - price <= 0: False
2025-07-11 07:25:39,497 - [BITVAVO] - root - INFO - TRADE ATTEMPT - SUI/EUR: Current price: 2.********
2025-07-11 07:25:39,604 - [BITVAVO] - root - INFO - Available balance for EUR: 4544.********
2025-07-11 07:25:39,613 - [BITVAVO] - root - INFO - Loaded market info for 176 trading pairs
2025-07-11 07:25:39,615 - [BITVAVO] - root - INFO - Calculated position size for SUI/EUR: 15.******** (using 1% of 4544.82, accounting for fees)
2025-07-11 07:25:39,615 - [BITVAVO] - root - INFO - Calculated position size: 15.******** SUI
2025-07-11 07:25:39,616 - [BITVAVO] - root - INFO - Entering position for SUI/EUR: 15.******** units at 2.******** (value: 45.******** EUR)
2025-07-11 07:25:39,616 - [BITVAVO] - root - INFO - Executing live market buy order for SUI/EUR
2025-07-11 07:25:39,670 - [BITVAVO] - root - INFO - Adjusted base amount for SUI/EUR: 15.******** -> 15.********
2025-07-11 07:25:39,717 - [BITVAVO] - root - ERROR - Error creating market buy order: [<class 'decimal.ConversionSyntax'>]
2025-07-11 07:25:39,718 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Failed to create order for SUI/EUR
2025-07-11 07:25:39,720 - [BITVAVO] - root - ERROR - Trade failed: BUY SUI/EUR, amount=15.********, price=2.********, reason=Failed to create order for SUI/EUR
2025-07-11 07:25:39,721 - [BITVAVO] - root - ERROR - Trade failed: UNKNOWN SUI/EUR, amount=0.********, price=0.********, reason=Failed to create order for SUI/EUR
2025-07-11 07:25:39,721 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Failed to enter position
2025-07-11 07:25:39,721 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Error type: order_creation_failed
2025-07-11 07:25:39,721 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Error reason: Failed to create order for SUI/EUR
2025-07-11 07:25:39,722 - [BITVAVO] - root - WARNING - HIGH-PRIORITY ASSET REJECTED: SUI/EUR - Failed to create order for SUI/EUR
2025-07-11 07:25:39,723 - [BITVAVO] - root - ERROR - Trade failed: UNKNOWN SUI/EUR, amount=0.********, price=0.********, reason=Failed to create order for SUI/EUR
2025-07-11 07:25:39,723 - [BITVAVO] - root - INFO - Single-asset trade result logged to trade log file
2025-07-11 07:25:39,724 - [BITVAVO] - root - ERROR - Trade execution failed: {'success': False, 'reason': 'Failed to create order for SUI/EUR', 'symbol': 'SUI/EUR', 'error_type': 'order_creation_failed'}
2025-07-11 07:25:39,782 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 400 Bad Request"
2025-07-11 07:25:39,783 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Can't parse entities: can't find end of the entity starting at byte offset 92
2025-07-11 07:25:39,829 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:25:39,833 - [BITVAVO] - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-11 07:25:39,833 - [BITVAVO] - root - INFO - Asset scores (sorted by score):
2025-07-11 07:25:39,833 - [BITVAVO] - root - INFO -   SUI/EUR: score=11.0, status=SELECTED, weight=1.00
2025-07-11 07:25:39,833 - [BITVAVO] - root - INFO -   XRP/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,833 - [BITVAVO] - root - INFO -   AAVE/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,834 - [BITVAVO] - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,834 - [BITVAVO] - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,834 - [BITVAVO] - root - INFO -   LINK/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,834 - [BITVAVO] - root - INFO -   DOGE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,834 - [BITVAVO] - root - INFO -   SOL/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,834 - [BITVAVO] - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,834 - [BITVAVO] - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,835 - [BITVAVO] - root - INFO -   AVAX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,835 - [BITVAVO] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,835 - [BITVAVO] - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,835 - [BITVAVO] - root - INFO -   DOT/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:25:39,835 - [BITVAVO] - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-11 07:25:39,836 - [BITVAVO] - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:25:39,882 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:25:39,885 - [BITVAVO] - root - INFO - Strategy execution completed successfully in 37.10 seconds
2025-07-11 07:25:39,888 - [BITVAVO] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-11 07:27:23,172 - [BITVAVO] - root - INFO - Received signal 2, shutting down...
2025-07-11 07:27:24,170 - [BITVAVO] - root - INFO - Received signal 2, shutting down...
2025-07-11 07:27:24,171 - [BITVAVO] - root - WARNING - Service is not running
